# CNN+XGBoost集成模型扩展

本扩展在原有MFDSMC框架基础上，添加了CNN处理DNA序列的功能，将CNN提取的序列特征与传统的146维特征拼接后输入XGBoost进行预测。

## 🚀 新增功能

### 1. CNN序列特征提取
- **输入**: 128bp DNA序列
- **编码**: One-hot编码 (4 × 128)
- **架构**: 多尺度卷积神经网络
- **输出**: 128维序列特征向量

### 2. 特征融合
- **传统特征**: 146维 (synMall注释特征)
- **CNN特征**: 128维 (序列特征)
- **融合特征**: 274维 (146 + 128)

### 3. 集成预测
- **预处理**: 缺失值填充 + MinMax归一化
- **模型**: XGBoost分类器
- **加速**: 支持GPU训练

## 📁 新增文件结构

```
code/
├── dna_cnn_module.py           # CNN模块核心代码
├── train_cnn_xgb_ensemble.py   # 集成模型训练脚本
├── test_cnn_xgb_ensemble.py    # 集成模型测试脚本
└── cnn_xgb_demo.py            # 快速演示脚本

model/
├── dna_cnn_model.pth          # 训练好的CNN模型
├── cnn_xgb_ensemble_model.pkl # 集成XGBoost模型
├── cnn_xgb_imputer.pkl        # 数据预处理器
├── cnn_xgb_scaler.pkl         # 归一化器
└── cnn_xgb_feature_names.json # 特征名称配置

results/
├── cnn_xgb_ensemble_test_results.json  # 详细测试结果
└── cnn_xgb_ensemble_summary.csv        # 结果摘要
```

## 🛠️ 环境配置

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

新增的依赖包括:
- `torch>=1.9.0` - PyTorch深度学习框架
- `numpy>=1.21.0` - 数值计算
- `matplotlib>=3.3.0` - 可视化
- `seaborn>=0.11.0` - 统计可视化

### 2. GPU支持 (可选)
如果有NVIDIA GPU，建议安装CUDA版本的PyTorch以加速训练:
```bash
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

## 🎯 使用方法

### 1. 快速演示
```bash
python code/cnn_xgb_demo.py
```
这个脚本会展示:
- 数据加载和预处理过程
- CNN特征提取演示
- 特征融合过程
- 模型架构说明

### 2. 完整训练
```bash
python code/train_cnn_xgb_ensemble.py
```
训练过程包括:
- CNN模型训练 (30个epoch)
- 特征提取和融合
- XGBoost模型训练
- 模型保存

### 3. 模型测试
```bash
python code/test_cnn_xgb_ensemble.py
```
测试功能:
- 加载训练好的模型
- 在测试集上评估性能
- 与原始模型比较
- 保存结果报告

## 🧬 数据格式

### DNA序列文件格式
```
chr20	37377139	C	T	CTGGGCGGACACTGCGCCGAGGGGCGGGGCTGGACGCGCGCTCCAAGATGGCGGCGAACGTGTTC...
chr20	45771712	C	T	GATTCCTCAGGCTCCGCGTGCATGTGATTTTCCCCTTCTCTGCCTCGCAGGACACCACGACGTCC...
```
格式说明:
- 列1: 染色体
- 列2: 位置
- 列3: 参考碱基
- 列4: 突变碱基
- 列5: 128bp DNA序列

### 传统特征文件
使用原有的146维特征文件格式，包含synMall注释的各种生物信息学特征。

## 🏗️ 模型架构详解

### CNN模块
```python
class DNACNNModel(nn.Module):
    def __init__(self):
        # 多尺度卷积层 (kernel_size: 3, 5, 7)
        self.convs = nn.ModuleList([...])
        # 批归一化
        self.batch_norms = nn.ModuleList([...])
        # 全局最大池化
        self.global_max_pool = nn.AdaptiveMaxPool1d(1)
        # 全连接层
        self.fc1 = nn.Linear(total_filters, 256)
        self.fc2 = nn.Linear(256, feature_dim)
```

### 特征融合流程
1. **DNA序列** → One-hot编码 → CNN → 128维特征
2. **传统特征** → 缺失值处理 → 146维特征
3. **特征拼接** → [146 + 128] = 274维
4. **预处理** → 归一化 → XGBoost

### XGBoost配置
```python
XGBClassifier(
    n_estimators=1000,      # 1000棵树
    learning_rate=0.1,      # 学习率
    max_depth=6,           # 最大深度
    tree_method='gpu_hist', # GPU加速
    random_state=42
)
```

## 📊 性能评估

### 评估指标
- **AUC**: ROC曲线下面积
- **AUPR**: 精确率-召回率曲线下面积
- **ACC**: 准确率
- **F1**: F1分数
- **MCC**: 马修斯相关系数
- **Sen**: 敏感性 (召回率)
- **Spe**: 特异性
- **Pre**: 精确率

### 预期改进
通过添加CNN序列特征，预期在以下方面有所改进:
- 更好地捕获DNA序列中的局部模式
- 识别重要的序列motif
- 提高对同义突变功能性的预测准确性

## 🔧 自定义配置

### 修改CNN架构
在 `dna_cnn_module.py` 中可以调整:
- `num_filters`: 卷积核数量
- `filter_sizes`: 卷积核大小
- `feature_dim`: 输出特征维度
- `dropout_rate`: Dropout比率

### 修改训练参数
在 `train_cnn_xgb_ensemble.py` 中可以调整:
- `cnn_epochs`: CNN训练轮数
- `batch_size`: 批处理大小
- `learning_rate`: 学习率
- `test_size`: 测试集比例

## 🐛 故障排除

### 常见问题

1. **GPU内存不足**
   ```python
   # 减小批处理大小
   batch_size = 16  # 默认32
   
   # 或使用CPU训练
   use_gpu = False
   ```

2. **序列长度不一致**
   - 所有序列会自动填充或截断到128bp
   - 不足的用'N'填充，超出的截断

3. **特征维度不匹配**
   - 检查传统特征文件格式
   - 确保特征名称与配置文件一致

4. **模型加载失败**
   - 确保先运行训练脚本
   - 检查模型文件路径

## 📈 结果分析

训练完成后，查看以下文件了解模型性能:
- `./model/cnn_xgb_training_report.json`: 训练报告
- `./results/cnn_xgb_ensemble_summary.csv`: 测试结果摘要
- `./results/cnn_xgb_ensemble_test_results.json`: 详细结果

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个集成模型！

## 📄 许可证

遵循原项目的许可证条款。
