import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
import joblib
import json
import os
from pathlib import Path
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score
import warnings
warnings.filterwarnings("ignore")

# 确保模型目录存在
Path("model").mkdir(exist_ok=True)

# 设备设置
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"使用设备: {device}")

# 软决策树类定义
class SoftDecisionTree(nn.Module):
    def __init__(self, input_dim, depth=5, n_trees=10):
        super(SoftDecisionTree, self).__init__()
        self.depth = depth
        self.n_trees = n_trees
        self.input_dim = input_dim
        
        # 决策节点
        self.decision_layers = nn.ModuleList([
            nn.Linear(input_dim, 2**i) for i in range(depth)
        ])
        
        # 叶子节点
        self.leaf_layer = nn.Linear(input_dim, 2**depth)
        
    def forward(self, x):
        batch_size = x.size(0)
        path_prob = torch.ones(batch_size, 1).to(x.device)
        
        for i, layer in enumerate(self.decision_layers):
            decision = torch.sigmoid(layer(x))
            left_prob = 1 - decision
            right_prob = decision
            
            if i == 0:
                path_prob = torch.cat([left_prob, right_prob], dim=1)
            else:
                new_path_prob = torch.zeros(batch_size, 2**(i+2)).to(x.device)
                for j in range(2**(i+1)):
                    new_path_prob[:, 2*j] = path_prob[:, j] * left_prob[:, j % (2**i)]
                    new_path_prob[:, 2*j+1] = path_prob[:, j] * right_prob[:, j % (2**i)]
                path_prob = new_path_prob
        
        leaf_values = self.leaf_layer(x)
        output = torch.sum(path_prob * leaf_values, dim=1, keepdim=True)
        return output

class SoftRandomForest(nn.Module):
    def __init__(self, input_dim, n_trees=10, depth=5):
        super(SoftRandomForest, self).__init__()
        self.n_trees = n_trees
        self.trees = nn.ModuleList([
            SoftDecisionTree(input_dim, depth) for _ in range(n_trees)
        ])
        
    def forward(self, x):
        outputs = [tree(x) for tree in self.trees]
        return torch.mean(torch.stack(outputs), dim=0)

class SoftXGBoost(nn.Module):
    def __init__(self, input_dim, n_estimators=10, depth=3):
        super(SoftXGBoost, self).__init__()
        self.n_estimators = n_estimators
        self.trees = nn.ModuleList([
            SoftDecisionTree(input_dim, depth) for _ in range(n_estimators)
        ])
        self.learning_rate = 0.1
        
    def forward(self, x):
        output = torch.zeros(x.size(0), 1).to(x.device)
        for tree in self.trees:
            output += self.learning_rate * tree(x)
        return output

class SoftEnsemble(nn.Module):
    def __init__(self, input_dim, rf_trees=10, xgb_estimators=10):
        super(SoftEnsemble, self).__init__()
        self.rf_model = SoftRandomForest(input_dim, rf_trees)
        self.xgb_model = SoftXGBoost(input_dim, xgb_estimators)
        self.ensemble_layer = nn.Linear(2, 1)
        
    def forward(self, x):
        rf_out = self.rf_model(x)
        xgb_out = self.xgb_model(x)
        combined = torch.cat([rf_out, xgb_out], dim=1)
        return self.ensemble_layer(combined)

def save_model_with_metadata(model, model_name, scaler, feature_names, model_params, performance_metrics):
    """保存模型及其元数据为.model格式"""
    model_data = {
        'model_state_dict': model.state_dict(),
        'model_class': model.__class__.__name__,
        'model_params': model_params,
        'scaler': scaler,
        'feature_names': feature_names,
        'performance_metrics': performance_metrics,
        'pytorch_version': torch.__version__,
        'numpy_version': np.__version__
    }
    
    model_path = f"model/{model_name}.model"
    joblib.dump(model_data, model_path)
    print(f"✓ 模型已保存: {model_path}")
    return model_path

def load_model_from_file(model_path, input_dim):
    """从.model文件加载模型"""
    model_data = joblib.load(model_path)
    
    # 重建模型
    if model_data['model_class'] == 'SoftEnsemble':
        model = SoftEnsemble(input_dim, **model_data['model_params'])
    elif model_data['model_class'] == 'SoftRandomForest':
        model = SoftRandomForest(input_dim, **model_data['model_params'])
    elif model_data['model_class'] == 'SoftXGBoost':
        model = SoftXGBoost(input_dim, **model_data['model_params'])
    
    model.load_state_dict(model_data['model_state_dict'])
    
    return model, model_data['scaler'], model_data['feature_names'], model_data['performance_metrics']

def train_model(model, train_loader, val_loader, epochs=500, lr=0.001, patience=50):
    """训练模型"""
    optimizer = optim.Adam(model.parameters(), lr=lr)
    criterion = nn.MSELoss()
    
    best_val_loss = float('inf')
    patience_counter = 0
    
    for epoch in range(epochs):
        # 训练
        model.train()
        train_loss = 0
        for batch_x, batch_y in train_loader:
            optimizer.zero_grad()
            outputs = model(batch_x)
            loss = criterion(outputs, batch_y)
            loss.backward()
            optimizer.step()
            train_loss += loss.item()
        
        # 验证
        model.eval()
        val_loss = 0
        with torch.no_grad():
            for batch_x, batch_y in val_loader:
                outputs = model(batch_x)
                loss = criterion(outputs, batch_y)
                val_loss += loss.item()
        
        train_loss /= len(train_loader)
        val_loss /= len(val_loader)
        
        if (epoch + 1) % 100 == 0:
            print(f"Epoch [{epoch+1}/{epochs}], Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}")
        
        # 早停
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
        else:
            patience_counter += 1
            if patience_counter >= patience:
                print(f"早停于第 {epoch+1} 轮，最佳验证损失: {best_val_loss:.4f}")
                break
    
    return best_val_loss

def evaluate_model(model, x, y, model_name):
    """模型评估"""
    model.eval()
    with torch.no_grad():
        preds = model(x)
        mse = nn.MSELoss()(preds, y).item()
    
    # 安全的numpy转换
    try:
        preds_np = preds.cpu().numpy()
        y_np = y.cpu().numpy()
    except:
        preds_np = np.array(preds.cpu().detach().tolist())
        y_np = np.array(y.cpu().detach().tolist())
    
    r2 = r2_score(y_np, preds_np)
    print(f"{model_name} - MSE: {mse:.4f}, R²: {r2:.4f}")
    
    return preds_np, mse, r2

def main():
    """主训练函数"""
    print("开始训练软决策树集成模型...")
    
    # 生成示例数据（您可以替换为实际数据）
    print("生成数据集...")
    np.random.seed(42)
    n_samples = 2000
    n_features = 73  # 与您的feat73_cols对应
    
    X = np.random.randn(n_samples, n_features)
    y = (X[:, 0] + X[:, 1] * X[:, 2] + np.sin(X[:, 3]) + 
         0.1 * np.random.randn(n_samples)).reshape(-1, 1)
    
    # 特征名称
    feature_names = [f"feat_{i+1}" for i in range(n_features)]
    
    # 数据分割
    X_train, X_temp, y_train, y_temp = train_test_split(X, y, test_size=0.4, random_state=42)
    X_val, X_test, y_val, y_test = train_test_split(X_temp, y_temp, test_size=0.5, random_state=42)
    
    # 数据标准化
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_val_scaled = scaler.transform(X_val)
    X_test_scaled = scaler.transform(X_test)
    
    # 转换为tensor
    X_train_tensor = torch.FloatTensor(X_train_scaled).to(device)
    y_train_tensor = torch.FloatTensor(y_train).to(device)
    X_val_tensor = torch.FloatTensor(X_val_scaled).to(device)
    y_val_tensor = torch.FloatTensor(y_val).to(device)
    X_test_tensor = torch.FloatTensor(X_test_scaled).to(device)
    y_test_tensor = torch.FloatTensor(y_test).to(device)
    
    print(f"训练集: {X_train_tensor.shape}, 验证集: {X_val_tensor.shape}, 测试集: {X_test_tensor.shape}")
    
    # 创建数据加载器
    train_dataset = torch.utils.data.TensorDataset(X_train_tensor, y_train_tensor)
    val_dataset = torch.utils.data.TensorDataset(X_val_tensor, y_val_tensor)
    train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=32, shuffle=True)
    val_loader = torch.utils.data.DataLoader(val_dataset, batch_size=32, shuffle=False)
    
    # 训练各个模型
    models_to_train = [
        ("SoftRandomForest", SoftRandomForest(n_features, n_trees=10, depth=5), {"n_trees": 10, "depth": 5}),
        ("SoftXGBoost", SoftXGBoost(n_features, n_estimators=10, depth=3), {"n_estimators": 10, "depth": 3}),
        ("SoftEnsemble", SoftEnsemble(n_features, rf_trees=10, xgb_estimators=10), {"rf_trees": 10, "xgb_estimators": 10})
    ]
    
    trained_models = {}
    
    for model_name, model, model_params in models_to_train:
        print(f"\n训练{model_name}...")
        model = model.to(device)
        
        best_val_loss = train_model(model, train_loader, val_loader)
        
        # 评估模型
        preds, mse, r2 = evaluate_model(model, X_test_tensor, y_test_tensor, model_name)
        
        # 性能指标
        performance_metrics = {
            'test_mse': mse,
            'test_r2': r2,
            'best_val_loss': best_val_loss
        }
        
        # 保存模型
        model_file = save_model_with_metadata(
            model, 
            f"{model_name.lower()}_feat{n_features}",
            scaler,
            feature_names,
            model_params,
            performance_metrics
        )
        
        trained_models[model_name] = {
            'model': model,
            'file_path': model_file,
            'performance': performance_metrics
        }
    
    # 保存最佳模型信息
    best_model_name = max(trained_models.keys(), 
                         key=lambda x: trained_models[x]['performance']['test_r2'])
    
    best_model_info = {
        'best_model': best_model_name,
        'best_performance': trained_models[best_model_name]['performance'],
        'all_models': {name: info['performance'] for name, info in trained_models.items()}
    }
    
    with open('model/best_model_info.json', 'w') as f:
        json.dump(best_model_info, f, indent=2)
    
    print(f"\n✅ 训练完成！")
    print(f"最佳模型: {best_model_name}")
    print(f"最佳R²: {trained_models[best_model_name]['performance']['test_r2']:.4f}")
    print(f"所有模型文件已保存到 model/ 目录")
    
    return trained_models

if __name__ == "__main__":
    trained_models = main()

