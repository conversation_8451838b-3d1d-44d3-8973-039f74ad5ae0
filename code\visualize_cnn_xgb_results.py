"""
CNN+XGBoost集成模型结果可视化脚本
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import json
import os
from pathlib import Path
import warnings
warnings.filterwarnings("ignore")

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def plot_feature_importance():
    """绘制特征重要性图"""
    
    print("绘制特征重要性分析...")
    
    # 检查是否有训练好的模型
    model_file = './model/cnn_xgb_ensemble_model.pkl'
    if not os.path.exists(model_file):
        print("未找到训练好的模型，请先运行训练脚本")
        return
    
    try:
        import joblib
        from train_cnn_xgb_ensemble import CNNXGBEnsemble
        
        # 加载模型
        ensemble = CNNXGBEnsemble()
        ensemble.load_trained_model('./model')
        
        # 获取特征重要性
        feature_importance = ensemble.xgb_model.feature_importances_
        
        # 加载特征名称
        with open('./model/cnn_xgb_feature_names.json', 'r') as f:
            feature_info = json.load(f)
        
        feature_names = feature_info['all_features']
        
        # 创建特征重要性DataFrame
        importance_df = pd.DataFrame({
            'feature': feature_names,
            'importance': feature_importance
        }).sort_values('importance', ascending=False)
        
        # 标记特征类型
        importance_df['feature_type'] = importance_df['feature'].apply(
            lambda x: 'CNN特征' if x.startswith('cnn_feat_') else '传统特征'
        )
        
        # 绘制总体特征重要性
        plt.figure(figsize=(15, 10))
        
        # 子图1: Top 30特征重要性
        plt.subplot(2, 2, 1)
        top_features = importance_df.head(30)
        colors = ['#FF6B6B' if ft == 'CNN特征' else '#4ECDC4' for ft in top_features['feature_type']]
        
        bars = plt.barh(range(len(top_features)), top_features['importance'], color=colors)
        plt.yticks(range(len(top_features)), top_features['feature'], fontsize=8)
        plt.xlabel('特征重要性')
        plt.title('Top 30 特征重要性')
        plt.gca().invert_yaxis()
        
        # 添加图例
        from matplotlib.patches import Patch
        legend_elements = [Patch(facecolor='#FF6B6B', label='CNN特征'),
                          Patch(facecolor='#4ECDC4', label='传统特征')]
        plt.legend(handles=legend_elements, loc='lower right')
        
        # 子图2: 特征类型重要性分布
        plt.subplot(2, 2, 2)
        type_importance = importance_df.groupby('feature_type')['importance'].agg(['sum', 'mean', 'count'])
        
        x = range(len(type_importance))
        plt.bar(x, type_importance['sum'], color=['#4ECDC4', '#FF6B6B'], alpha=0.7)
        plt.xticks(x, type_importance.index)
        plt.ylabel('总重要性')
        plt.title('不同类型特征的总重要性')
        
        # 在柱子上添加数值
        for i, v in enumerate(type_importance['sum']):
            plt.text(i, v + 0.001, f'{v:.3f}', ha='center', va='bottom')
        
        # 子图3: 特征重要性分布直方图
        plt.subplot(2, 2, 3)
        plt.hist(importance_df[importance_df['feature_type'] == '传统特征']['importance'], 
                bins=20, alpha=0.7, label='传统特征', color='#4ECDC4')
        plt.hist(importance_df[importance_df['feature_type'] == 'CNN特征']['importance'], 
                bins=20, alpha=0.7, label='CNN特征', color='#FF6B6B')
        plt.xlabel('特征重要性')
        plt.ylabel('频次')
        plt.title('特征重要性分布')
        plt.legend()
        
        # 子图4: 累积重要性
        plt.subplot(2, 2, 4)
        cumsum = importance_df['importance'].cumsum()
        plt.plot(range(len(cumsum)), cumsum, 'b-', linewidth=2)
        plt.axhline(y=0.8, color='r', linestyle='--', alpha=0.7, label='80%重要性')
        plt.axhline(y=0.9, color='orange', linestyle='--', alpha=0.7, label='90%重要性')
        plt.xlabel('特征数量')
        plt.ylabel('累积重要性')
        plt.title('特征重要性累积分布')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图片
        os.makedirs('./results/plots', exist_ok=True)
        plt.savefig('./results/plots/feature_importance_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # 保存特征重要性数据
        importance_df.to_csv('./results/cnn_xgb_feature_importance.csv', index=False)
        
        # 打印统计信息
        print(f"\n特征重要性统计:")
        print(f"总特征数: {len(importance_df)}")
        print(f"传统特征数: {sum(importance_df['feature_type'] == '传统特征')}")
        print(f"CNN特征数: {sum(importance_df['feature_type'] == 'CNN特征')}")
        print(f"\n各类型特征重要性:")
        print(type_importance)
        
    except Exception as e:
        print(f"绘制特征重要性时出错: {e}")

def plot_performance_comparison():
    """绘制性能对比图"""
    
    print("绘制性能对比分析...")
    
    # 检查结果文件
    cnn_xgb_file = './results/cnn_xgb_ensemble_summary.csv'
    original_files = [
        './results/test1_xgb_rf_ada_results.csv',
        './results/test2_xgb_rf_ada_results.csv'
    ]
    
    if not os.path.exists(cnn_xgb_file):
        print("未找到CNN+XGBoost结果文件，请先运行测试脚本")
        return
    
    # 读取CNN+XGBoost结果
    cnn_xgb_results = pd.read_csv(cnn_xgb_file)
    
    # 准备对比数据
    comparison_data = []
    
    for i, original_file in enumerate(original_files):
        test_name = f'test{i+1}'
        
        if os.path.exists(original_file):
            original_results = pd.read_csv(original_file)
            cnn_row = cnn_xgb_results[cnn_xgb_results['test_set'] == test_name]
            
            if not cnn_row.empty and not original_results.empty:
                metrics = ['AUC', 'AUPR', 'ACC', 'F1', 'MCC']
                
                for metric in metrics:
                    if metric in original_results.columns and metric in cnn_row.columns:
                        comparison_data.append({
                            'test_set': test_name,
                            'metric': metric,
                            'original': original_results[metric].iloc[0],
                            'cnn_xgb': cnn_row[metric].iloc[0],
                            'improvement': cnn_row[metric].iloc[0] - original_results[metric].iloc[0]
                        })
    
    if not comparison_data:
        print("没有找到可比较的结果数据")
        return
    
    comparison_df = pd.DataFrame(comparison_data)
    
    # 绘制对比图
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 子图1: 性能对比柱状图
    ax1 = axes[0, 0]
    metrics = comparison_df['metric'].unique()
    x = np.arange(len(metrics))
    width = 0.35
    
    # 计算平均性能
    original_means = [comparison_df[comparison_df['metric'] == m]['original'].mean() for m in metrics]
    cnn_xgb_means = [comparison_df[comparison_df['metric'] == m]['cnn_xgb'].mean() for m in metrics]
    
    bars1 = ax1.bar(x - width/2, original_means, width, label='原始模型', color='#4ECDC4', alpha=0.8)
    bars2 = ax1.bar(x + width/2, cnn_xgb_means, width, label='CNN+XGBoost', color='#FF6B6B', alpha=0.8)
    
    ax1.set_xlabel('评估指标')
    ax1.set_ylabel('性能值')
    ax1.set_title('模型性能对比 (平均值)')
    ax1.set_xticks(x)
    ax1.set_xticklabels(metrics)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 在柱子上添加数值
    for bars in [bars1, bars2]:
        for bar in bars:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.005,
                    f'{height:.3f}', ha='center', va='bottom', fontsize=9)
    
    # 子图2: 改进幅度
    ax2 = axes[0, 1]
    improvements = [comparison_df[comparison_df['metric'] == m]['improvement'].mean() for m in metrics]
    colors = ['green' if imp > 0 else 'red' for imp in improvements]
    
    bars = ax2.bar(metrics, improvements, color=colors, alpha=0.7)
    ax2.set_xlabel('评估指标')
    ax2.set_ylabel('改进幅度')
    ax2.set_title('CNN+XGBoost相对原始模型的改进')
    ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    ax2.grid(True, alpha=0.3)
    
    # 在柱子上添加数值
    for bar, imp in zip(bars, improvements):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + (0.001 if height > 0 else -0.003),
                f'{imp:+.3f}', ha='center', va='bottom' if height > 0 else 'top', fontsize=9)
    
    # 子图3: 不同测试集的性能对比
    ax3 = axes[1, 0]
    test_sets = comparison_df['test_set'].unique()
    
    for i, test_set in enumerate(test_sets):
        test_data = comparison_df[comparison_df['test_set'] == test_set]
        x_pos = np.arange(len(test_data)) + i * 0.4
        
        ax3.plot(x_pos, test_data['original'], 'o-', label=f'{test_set} 原始', alpha=0.7)
        ax3.plot(x_pos, test_data['cnn_xgb'], 's-', label=f'{test_set} CNN+XGBoost', alpha=0.7)
    
    ax3.set_xlabel('指标')
    ax3.set_ylabel('性能值')
    ax3.set_title('不同测试集的详细对比')
    ax3.set_xticks(np.arange(len(metrics)))
    ax3.set_xticklabels(metrics)
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 子图4: 改进幅度热力图
    ax4 = axes[1, 1]
    pivot_data = comparison_df.pivot(index='test_set', columns='metric', values='improvement')
    
    sns.heatmap(pivot_data, annot=True, cmap='RdYlGn', center=0, 
                fmt='.3f', ax=ax4, cbar_kws={'label': '改进幅度'})
    ax4.set_title('改进幅度热力图')
    ax4.set_xlabel('评估指标')
    ax4.set_ylabel('测试集')
    
    plt.tight_layout()
    
    # 保存图片
    plt.savefig('./results/plots/performance_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 保存对比数据
    comparison_df.to_csv('./results/performance_comparison.csv', index=False)
    
    # 打印统计信息
    print(f"\n性能对比统计:")
    print(f"对比指标数: {len(metrics)}")
    print(f"测试集数: {len(test_sets)}")
    print(f"\n平均改进幅度:")
    for metric in metrics:
        avg_imp = comparison_df[comparison_df['metric'] == metric]['improvement'].mean()
        print(f"  {metric}: {avg_imp:+.4f}")

def plot_training_curves():
    """绘制训练曲线（如果有训练日志）"""
    
    print("绘制训练曲线...")
    
    # 检查是否有训练报告
    report_file = './model/cnn_xgb_training_report.json'
    if not os.path.exists(report_file):
        print("未找到训练报告文件")
        return
    
    with open(report_file, 'r') as f:
        report = json.load(f)
    
    # 创建训练信息可视化
    plt.figure(figsize=(12, 8))
    
    # 子图1: 特征维度对比
    plt.subplot(2, 2, 1)
    feature_dims = [
        report['traditional_feature_dim'],
        report['cnn_feature_dim'],
        report['total_feature_dim']
    ]
    labels = ['传统特征', 'CNN特征', '总特征']
    colors = ['#4ECDC4', '#FF6B6B', '#45B7D1']
    
    bars = plt.bar(labels, feature_dims, color=colors, alpha=0.8)
    plt.ylabel('特征维度')
    plt.title('特征维度对比')
    
    for bar, dim in zip(bars, feature_dims):
        plt.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 2,
                f'{dim}', ha='center', va='bottom', fontweight='bold')
    
    # 子图2: 训练/测试样本分布
    plt.subplot(2, 2, 2)
    sample_counts = [report['train_samples'], report['test_samples']]
    labels = ['训练样本', '测试样本']
    colors = ['#95E1D3', '#F38BA8']
    
    plt.pie(sample_counts, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
    plt.title('样本分布')
    
    # 子图3: 训练集性能
    plt.subplot(2, 2, 3)
    train_metrics = report['train_metrics']
    metrics = list(train_metrics.keys())
    values = list(train_metrics.values())
    
    plt.barh(metrics, values, color='#4ECDC4', alpha=0.8)
    plt.xlabel('性能值')
    plt.title('训练集性能')
    plt.xlim(0, 1)
    
    for i, v in enumerate(values):
        plt.text(v + 0.01, i, f'{v:.3f}', va='center')
    
    # 子图4: 测试集性能
    plt.subplot(2, 2, 4)
    test_metrics = report['test_metrics']
    metrics = list(test_metrics.keys())
    values = list(test_metrics.values())
    
    plt.barh(metrics, values, color='#FF6B6B', alpha=0.8)
    plt.xlabel('性能值')
    plt.title('测试集性能')
    plt.xlim(0, 1)
    
    for i, v in enumerate(values):
        plt.text(v + 0.01, i, f'{v:.3f}', va='center')
    
    plt.tight_layout()
    
    # 保存图片
    plt.savefig('./results/plots/training_summary.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """主函数"""
    print("=== CNN+XGBoost集成模型结果可视化 ===")
    
    # 创建结果目录
    os.makedirs('./results/plots', exist_ok=True)
    
    # 绘制各种分析图
    plot_feature_importance()
    plot_performance_comparison()
    plot_training_curves()
    
    print("\n=== 可视化完成 ===")
    print("图片已保存到 ./results/plots/ 目录")

if __name__ == "__main__":
    main()
