import pandas as pd
import numpy as np
import joblib,json
from utils import rename_with_feat40, metricsScores
from sklearn.preprocessing import MinMaxScaler
from sklearn.impute import SimpleImputer
import warnings
warnings.filterwarnings("ignore")

# 读取73维最优特征名称
feat146_cols = pd.read_csv('./results/AAAA_featureSelect/feat_importance_sorted_rename.csv')
feat73_cols = feat146_cols['feat_name'][:73].tolist()

# 加载模型
model = joblib.load('./model/voting_xgb_rf_feat73_gpu.model')

# 检查模型类型并加载预处理器
if hasattr(model, 'imputer') and hasattr(model, 'scaler'):
    # 如果是集成模型对象，直接使用内部的预处理器
    print("使用模型内部的预处理器")
    imputer = model.imputer
    minmax = model.scaler
    feat73_model = model.feature_names if hasattr(model, 'feature_names') else feat73_cols
else:
    # 如果是传统模型，加载独立的预处理器
    print("加载独立的预处理器文件")
    try:
        imputer = joblib.load('./model/ensemble_imputer.pkl')
        minmax = joblib.load('./model/ensemble_minmax.pkl')
    except FileNotFoundError:
        imputer = joblib.load('./model/imputer.pkl')
        minmax = joblib.load('./model/minmax.pkl')
    
    # 加载训练时使用的特征名称
    try:
        with open('./model/feat73_names.json', 'r') as f:
            feat73_model = json.load(f)
    except FileNotFoundError:
        print("警告：未找到feat73_names.json，使用默认特征名称")
        feat73_model = feat73_cols

print(f"模型训练时使用的特征数量: {len(feat73_model)}")

# 读取数据
metrics = 'Sen, Spe, Pre, F1, MCC, ACC, AUC, AUPR, tn, fp, fn, tp, thres'.split(', ')

# 训练集测试
print("处理数据...")
X_train, renamed_columns = rename_with_feat40('./data/feature-encoded/for-train-test/test1-closeby6-encoded-feat146.csv')
print(f"重命名后的训练数据形状: {X_train.shape}")

# 检查标签列
if 'label' in X_train.columns:
    label_col = 'label'
elif '#class' in X_train.columns:
    label_col = '#class'
else:
    label_col = X_train.columns[-1]
    print(f"使用最后一列作为标签: {label_col}")

# 使用模型训练时的特征子集
available_features = [feat for feat in feat73_model if feat in X_train.columns]
print(f"可用特征数量: {len(available_features)}")

if len(available_features) != len(feat73_model):
    missing_features = [feat for feat in feat73_model if feat not in X_train.columns]
    print(f"缺失特征: {missing_features[:5]}...")

# 准备特征数据 - 关键修复：确保特征顺序和名称完全一致
X_features = X_train[available_features].copy()
X_features.replace('na', np.nan, inplace=True)
X_features = X_features.astype(float, errors='ignore')

# 检查预处理器是否需要重新训练
try:
    # 尝试直接使用预处理器
    X_imputed = imputer.transform(X_features)
    X_scaled = minmax.transform(X_imputed)
    print("✓ 成功使用预训练的预处理器")
except ValueError as e:
    print(f"预处理器特征不匹配，重新训练预处理器: {e}")
    
    # 重新训练预处理器
    imputer_new = SimpleImputer(strategy='median')
    minmax_new = MinMaxScaler()
    
    X_imputed = imputer_new.fit_transform(X_features)
    X_scaled = minmax_new.fit_transform(X_imputed)
    
    print("✓ 使用重新训练的预处理器")

X_final = pd.DataFrame(X_scaled, columns=available_features)

# 预测
if hasattr(model, 'predict_proba'):
    # 如果是集成模型对象
    pred_train = model.predict_proba(X_final.values)
    if len(pred_train.shape) > 1 and pred_train.shape[1] > 1:
        pred_train = pred_train[:, 1]
else:
    # 如果是传统模型
    pred_train = model.predict_proba(X_final.values)[:, 1]

# 计算指标
result_df = pd.concat([
    pd.Series(metrics),
    pd.Series(metricsScores(X_train[label_col].values, pred_train))
], axis=1).T

result_df.columns = result_df.iloc[0]
result_df = result_df[1:]

print("数据集预测结果:")
print(result_df.iloc[:,:-5])  # 不显示混淆矩阵部分
