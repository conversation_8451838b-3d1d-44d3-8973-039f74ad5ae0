#!/usr/bin/env python3
"""
整合的训练+评估脚本
直接在训练集、测试集1、测试集2上训练并评估模型，无需保存模型文件
"""

import sys
import os
import pandas as pd
import numpy as np
import json
from sklearn.ensemble import RandomForestClassifier
from xgboost import XGBClassifier
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import MinMaxScaler
from sklearn.impute import SimpleImputer
from sklearn.metrics import roc_auc_score, classification_report
import warnings
warnings.filterwarnings("ignore")

# 添加code目录到路径
sys.path.append('./code')
sys.path.append('.')

try:
    from utils import rename_with_feat40, metricsScores
    print("✓ 成功导入utils模块")
except ImportError as e:
    print(f"✗ 导入utils失败: {e}")
    sys.exit(1)

def load_dna_sequences_from_folder(folder_path):
    """从指定文件夹加载DNA序列"""
    sequences = []
    labels = []
    
    # 尝试不同的文件名格式
    possible_files = [
        ("train_pos_seq.txt", "train_neg_seq.txt"),
        ("test1_pos_seq.txt", "test1_neg_seq.txt"), 
        ("test2_pos_seq.txt", "test2_neg_seq.txt"),
        ("pos_seq.txt", "neg_seq.txt")
    ]
    
    pos_file = neg_file = None
    for pos_name, neg_name in possible_files:
        pos_path = os.path.join(folder_path, pos_name)
        neg_path = os.path.join(folder_path, neg_name)
        if os.path.exists(pos_path) and os.path.exists(neg_path):
            pos_file, neg_file = pos_path, neg_path
            break
    
    if not pos_file or not neg_file:
        print(f"  ✗ 在 {folder_path} 中未找到序列文件")
        return [], []
    
    print(f"  正样本文件: {pos_file}")
    print(f"  负样本文件: {neg_file}")
    
    # 加载正样本
    try:
        with open(pos_file, 'r') as f:
            for line in f:
                parts = line.strip().split('\t')
                if len(parts) >= 5:
                    seq = parts[4]
                    sequences.append(seq)
                    labels.append(1)
        print(f"  ✓ 加载了 {sum(labels)} 个正样本")
    except Exception as e:
        print(f"  ✗ 加载正样本失败: {e}")
        return [], []
    
    # 加载负样本
    try:
        with open(neg_file, 'r') as f:
            for line in f:
                parts = line.strip().split('\t')
                if len(parts) >= 5:
                    seq = parts[4]
                    sequences.append(seq)
                    labels.append(0)
        print(f"  ✓ 加载了 {len(labels) - sum(labels)} 个负样本")
    except Exception as e:
        print(f"  ✗ 加载负样本失败: {e}")
        return [], []
    
    return sequences, labels

def encode_dna_sequence_simple(sequence, seq_length=128):
    """简单的DNA序列编码函数"""
    if len(sequence) > seq_length:
        sequence = sequence[:seq_length]
    elif len(sequence) < seq_length:
        sequence = sequence + 'N' * (seq_length - len(sequence))
    
    base_to_num = {'A': 0, 'T': 1, 'G': 2, 'C': 3, 'N': 4}
    encoded = [base_to_num.get(base, 4) for base in sequence.upper()]
    return encoded

def extract_sequence_features_simple(sequences):
    """简单的序列特征提取"""
    features = []
    for seq in sequences:
        encoded = encode_dna_sequence_simple(seq)
        seq_features = []
        
        # 碱基组成 (5维)
        for base_num in range(5):
            count = encoded.count(base_num)
            seq_features.append(count / len(encoded))
        
        # GC含量 (1维)
        gc_count = encoded.count(2) + encoded.count(3)
        seq_features.append(gc_count / len(encoded))
        
        # 连续碱基统计 (4维)
        for base_num in range(4):
            max_consecutive = 0
            current_consecutive = 0
            for e in encoded:
                if e == base_num:
                    current_consecutive += 1
                    max_consecutive = max(max_consecutive, current_consecutive)
                else:
                    current_consecutive = 0
            seq_features.append(max_consecutive / len(encoded))
        
        # 二核苷酸频率 (10维)
        dinucleotides = ['AA', 'AT', 'AG', 'AC', 'TT', 'TG', 'TC', 'GG', 'GC', 'CC']
        seq_str = ''.join([['A', 'T', 'G', 'C', 'N'][e] for e in encoded])
        for dinuc in dinucleotides:
            count = seq_str.count(dinuc)
            seq_features.append(count / max(1, len(seq_str) - 1))
        
        features.append(seq_features)
    
    return np.array(features)

def prepare_dataset(feature_file, seq_folder, feat_names):
    """准备单个数据集"""
    print(f"\n--- 准备数据集 ---")
    print(f"特征文件: {feature_file}")
    print(f"序列文件夹: {seq_folder}")
    
    # 1. 加载传统特征
    if not os.path.exists(feature_file):
        print(f"✗ 特征文件不存在: {feature_file}")
        return None
    
    feature_data, _ = rename_with_feat40(feature_file)
    print(f"✓ 传统特征数据形状: {feature_data.shape}")
    
    # 2. 加载DNA序列
    if not os.path.exists(seq_folder):
        print(f"✗ 序列文件夹不存在: {seq_folder}")
        return None
    
    sequences, seq_labels = load_dna_sequences_from_folder(seq_folder)
    if not sequences:
        print("✗ 序列加载失败")
        return None
    
    print(f"✓ 序列数量: {len(sequences)}")
    
    # 3. 数据对齐
    min_len = min(len(feature_data), len(sequences))
    feature_data = feature_data.iloc[:min_len]
    sequences = sequences[:min_len]
    seq_labels = seq_labels[:min_len]
    
    # 获取真实标签
    if '#class' in feature_data.columns:
        true_labels = feature_data['#class'].values
    else:
        true_labels = np.array(seq_labels)
    
    print(f"✓ 对齐后样本数: {min_len}")
    print(f"✓ 标签分布: 正样本={sum(true_labels)}, 负样本={len(true_labels)-sum(true_labels)}")
    
    # 4. 特征提取和组合
    print("提取序列特征...")
    seq_features = extract_sequence_features_simple(sequences)
    print(f"✓ 序列特征形状: {seq_features.shape}")
    
    # 处理传统特征
    X_traditional = feature_data[feat_names]
    X_traditional.replace('na', np.nan, inplace=True)
    X_traditional = X_traditional.astype(float, errors='ignore')
    print(f"✓ 传统特征形状: {X_traditional.shape}")
    
    # 组合特征
    seq_feature_names = [f'seq_feat_{i}' for i in range(seq_features.shape[1])]
    seq_df = pd.DataFrame(seq_features, columns=seq_feature_names, index=X_traditional.index)
    combined_features = pd.concat([X_traditional, seq_df], axis=1)
    print(f"✓ 组合特征形状: {combined_features.shape}")
    
    return {
        'features': combined_features,
        'labels': true_labels,
        'n_samples': min_len,
        'n_positive': sum(true_labels),
        'n_negative': len(true_labels) - sum(true_labels),
        'traditional_features': len(feat_names),
        'sequence_features': seq_features.shape[1],
        'total_features': combined_features.shape[1]
    }

def train_and_evaluate_model(train_data, test_datasets):
    """训练模型并在所有数据集上评估"""
    print(f"\n=== 开始训练和评估 ===")
    
    # 1. 准备训练数据
    print("1. 准备训练数据...")
    X_train_full = train_data['features']
    y_train_full = train_data['labels']
    
    # 数据预处理
    imputer = SimpleImputer(strategy='median')
    X_train_imputed = imputer.fit_transform(X_train_full)
    
    scaler = MinMaxScaler()
    X_train_scaled = scaler.fit_transform(X_train_imputed)
    
    print(f"✓ 训练数据预处理完成: {X_train_scaled.shape}")
    
    # 2. 训练模型
    print("2. 训练XGBoost模型...")
    model = XGBClassifier(
        n_estimators=100,
        learning_rate=0.1,
        max_depth=6,
        random_state=42,
        n_jobs=-1
    )
    
    model.fit(X_train_scaled, y_train_full)
    print("✓ 模型训练完成")
    
    # 3. 评估所有数据集
    results = []
    
    # 评估训练集
    print("\n3. 评估训练集...")
    train_pred = model.predict_proba(X_train_scaled)[:, 1]
    train_metrics = metricsScores(y_train_full, train_pred)
    
    train_result = {
        'dataset': '训练集',
        'n_samples': int(train_data['n_samples']),
        'n_positive': int(train_data['n_positive']),
        'n_negative': int(train_data['n_negative']),
        'metrics': {k: float(v) for k, v in zip(['Sen', 'Spe', 'Pre', 'F1', 'MCC', 'ACC', 'AUC', 'AUPR'], train_metrics[:8])}
    }
    results.append(train_result)
    
    print("训练集结果:")
    for name, value in train_result['metrics'].items():
        print(f"  {name}: {value:.4f}")
    
    # 评估测试集
    for test_name, test_data in test_datasets.items():
        print(f"\n4. 评估 {test_name}...")
        
        # 预处理测试数据
        X_test = test_data['features']
        y_test = test_data['labels']
        
        X_test_imputed = imputer.transform(X_test)
        X_test_scaled = scaler.transform(X_test_imputed)
        
        # 预测
        test_pred = model.predict_proba(X_test_scaled)[:, 1]
        test_metrics = metricsScores(y_test, test_pred)
        
        test_result = {
            'dataset': test_name,
            'n_samples': int(test_data['n_samples']),
            'n_positive': int(test_data['n_positive']),
            'n_negative': int(test_data['n_negative']),
            'metrics': {k: float(v) for k, v in zip(['Sen', 'Spe', 'Pre', 'F1', 'MCC', 'ACC', 'AUC', 'AUPR'], test_metrics[:8])}
        }
        results.append(test_result)
        
        print(f"{test_name}结果:")
        for name, value in test_result['metrics'].items():
            print(f"  {name}: {value:.4f}")
    
    return results

def save_results(results):
    """保存结果"""
    print(f"\n=== 保存结果 ===")
    
    # 创建结果目录
    os.makedirs('./results', exist_ok=True)
    
    # 保存详细结果
    with open('./results/integrated_evaluation_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    # 保存CSV摘要
    summary_data = []
    for result in results:
        row = {
            'dataset': result['dataset'],
            'n_samples': result['n_samples'],
            'n_positive': result['n_positive'],
            'n_negative': result['n_negative']
        }
        row.update(result['metrics'])
        summary_data.append(row)
    
    df_summary = pd.DataFrame(summary_data)
    df_summary.to_csv('./results/integrated_evaluation_summary.csv', index=False)
    
    print("✓ 结果已保存到:")
    print("  - ./results/integrated_evaluation_results.json")
    print("  - ./results/integrated_evaluation_summary.csv")

def main():
    """主函数"""
    print("🧬 整合训练+评估流程")
    print("=" * 60)
    
    # 1. 加载特征名称
    print("加载特征配置...")
    try:
        with open('./data/feat146.json', 'r') as f:
            feat146 = json.load(f)
        print(f"✓ 特征名称数量: {len(feat146)}")
    except Exception as e:
        print(f"✗ 加载特征名称失败: {e}")
        return 1
    
    # 2. 定义数据集配置
    datasets_config = {
        'train': {
            'feature_file': './data/feature-encoded/for-train-test/train-closeby6-encoded-feat146.csv',
            'seq_folder': './data/train_seq_cosmic'
        },
        'test1': {
            'feature_file': './data/feature-encoded/for-train-test/test1-closeby6-encoded-feat146.csv',
            'seq_folder': './data/test1_seq_cosmic'
        },
        'test2': {
            'feature_file': './data/feature-encoded/for-train-test/test2-closeby6-encoded-feat146.csv',
            'seq_folder': './data/test2_seq_cosmic'
        }
    }
    
    # 3. 准备所有数据集
    print("\n=== 准备所有数据集 ===")
    
    train_data = prepare_dataset(
        datasets_config['train']['feature_file'],
        datasets_config['train']['seq_folder'],
        feat146
    )
    
    if train_data is None:
        print("✗ 训练数据准备失败")
        return 1
    
    test_datasets = {}
    for test_name in ['test1', 'test2']:
        test_data = prepare_dataset(
            datasets_config[test_name]['feature_file'],
            datasets_config[test_name]['seq_folder'],
            feat146
        )
        if test_data is not None:
            test_datasets[f'测试集{test_name[-1]}'] = test_data
    
    if not test_datasets:
        print("✗ 没有可用的测试数据集")
        return 1
    
    # 4. 训练和评估
    results = train_and_evaluate_model(train_data, test_datasets)
    
    # 5. 保存结果
    save_results(results)
    
    # 6. 显示汇总
    print(f"\n=== 最终结果汇总 ===")
    print("=" * 80)
    print(f"{'数据集':<10} {'样本数':<8} {'正样本':<8} {'负样本':<8} {'AUC':<8} {'AUPR':<8} {'ACC':<8} {'F1':<8}")
    print("-" * 80)
    
    for result in results:
        print(f"{result['dataset']:<10} "
              f"{result['n_samples']:<8} "
              f"{result['n_positive']:<8} "
              f"{result['n_negative']:<8} "
              f"{result['metrics']['AUC']:<8.4f} "
              f"{result['metrics']['AUPR']:<8.4f} "
              f"{result['metrics']['ACC']:<8.4f} "
              f"{result['metrics']['F1']:<8.4f}")
    
    print("\n🎉 整合评估完成!")
    return 0

if __name__ == "__main__":
    exit(main())
