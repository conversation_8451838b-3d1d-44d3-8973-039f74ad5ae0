#!/usr/bin/env python3
"""
修复版训练+评估脚本
解决ID对齐、数据类型、交叉验证等关键问题
"""

import sys
import os
import pandas as pd
import numpy as np
import json
from sklearn.ensemble import RandomForestClassifier
from xgboost import XGBClassifier
from sklearn.model_selection import train_test_split, StratifiedKFold, GridSearchCV
from sklearn.preprocessing import MinMaxScaler
from sklearn.impute import SimpleImputer
from sklearn.metrics import roc_auc_score, classification_report
from collections import Counter
import hashlib
import warnings
warnings.filterwarnings("ignore")

# 添加code目录到路径
sys.path.append('./code')
sys.path.append('.')

try:
    from utils import rename_with_feat40, metricsScores
    print("✓ 成功导入utils模块")
except ImportError as e:
    print(f"✗ 导入utils失败: {e}")
    sys.exit(1)

def load_dna_sequences_from_folder(folder_path):
    """从指定文件夹加载DNA序列，返回带ID的数据结构"""
    sequence_data = []

    # 尝试不同的文件名格式
    possible_files = [
        ("train_pos_seq.txt", "train_neg_seq.txt"),
        ("test1_pos_seq.txt", "test1_neg_seq.txt"),
        ("test2_pos_seq.txt", "test2_neg_seq.txt"),
        ("pos_seq.txt", "neg_seq.txt")
    ]

    pos_file = neg_file = None
    for pos_name, neg_name in possible_files:
        pos_path = os.path.join(folder_path, pos_name)
        neg_path = os.path.join(folder_path, neg_name)
        if os.path.exists(pos_path) and os.path.exists(neg_path):
            pos_file, neg_file = pos_path, neg_path
            break

    if not pos_file or not neg_file:
        print(f"  ✗ 在 {folder_path} 中未找到序列文件")
        return pd.DataFrame()

    print(f"  正样本文件: {pos_file}")
    print(f"  负样本文件: {neg_file}")

    # 加载正样本
    try:
        with open(pos_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                parts = line.strip().split('\t')
                if len(parts) >= 5:
                    chrom, pos, ref, alt, seq = parts[:5]
                    # 创建唯一ID用于对齐检查
                    mutation_id = f"{chrom}:{pos}:{ref}>{alt}"
                    sequence_data.append({
                        'mutation_id': mutation_id,
                        'chrom': chrom,
                        'pos': int(pos),
                        'ref': ref,
                        'alt': alt,
                        'sequence': seq.upper(),  # 统一大写
                        'label': 1,
                        'file_source': 'positive',
                        'line_number': line_num
                    })
        print(f"  ✓ 加载了 {sum(1 for x in sequence_data if x['label'] == 1)} 个正样本")
    except Exception as e:
        print(f"  ✗ 加载正样本失败: {e}")
        return pd.DataFrame()

    # 加载负样本
    try:
        with open(neg_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                parts = line.strip().split('\t')
                if len(parts) >= 5:
                    chrom, pos, ref, alt, seq = parts[:5]
                    mutation_id = f"{chrom}:{pos}:{ref}>{alt}"
                    sequence_data.append({
                        'mutation_id': mutation_id,
                        'chrom': chrom,
                        'pos': int(pos),
                        'ref': ref,
                        'alt': alt,
                        'sequence': seq.upper(),
                        'label': 0,
                        'file_source': 'negative',
                        'line_number': line_num
                    })
        print(f"  ✓ 加载了 {sum(1 for x in sequence_data if x['label'] == 0)} 个负样本")
    except Exception as e:
        print(f"  ✗ 加载负样本失败: {e}")
        return pd.DataFrame()

    # 转换为DataFrame并检查数据质量
    df = pd.DataFrame(sequence_data)

    # 检查序列长度
    seq_lengths = df['sequence'].str.len()
    print(f"  序列长度统计: min={seq_lengths.min()}, max={seq_lengths.max()}, mean={seq_lengths.mean():.1f}")

    # 检查重复ID
    duplicate_ids = df['mutation_id'].duplicated().sum()
    if duplicate_ids > 0:
        print(f"  ⚠️  发现 {duplicate_ids} 个重复的mutation_id")
        df = df.drop_duplicates(subset=['mutation_id'], keep='first')
        print(f"  已去除重复，剩余 {len(df)} 个样本")

    return df

def validate_and_normalize_sequence(sequence, seq_length=128):
    """验证和标准化DNA序列"""
    # 转换为大写并移除空白字符
    sequence = str(sequence).upper().strip()

    # 验证序列只包含有效的DNA碱基
    valid_bases = set('ATGCN')
    invalid_bases = set(sequence) - valid_bases
    if invalid_bases:
        # 只在处理大量序列时打印警告，避免测试时过多输出
        if len(sequence) > 50:  # 只对长序列打印警告
            print(f"  ⚠️  序列包含无效碱基: {invalid_bases}")
        # 将无效碱基替换为N
        for invalid_base in invalid_bases:
            sequence = sequence.replace(invalid_base, 'N')

    # 调整序列长度
    if len(sequence) > seq_length:
        sequence = sequence[:seq_length]
    elif len(sequence) < seq_length:
        sequence = sequence + 'N' * (seq_length - len(sequence))

    return sequence

def extract_sequence_features_robust(sequences, seq_length=128):
    """鲁棒的序列特征提取"""
    print(f"  开始提取 {len(sequences)} 个序列的特征...")

    features = []
    feature_names = []

    # 定义特征名称
    base_names = ['A', 'T', 'G', 'C', 'N']
    dinuc_names = ['AA', 'AT', 'AG', 'AC', 'TT', 'TG', 'TC', 'GG', 'GC', 'CC']

    # 构建特征名称列表
    for base in base_names:
        feature_names.append(f'base_freq_{base}')
    feature_names.append('GC_content')
    for base in base_names[:4]:  # 不包括N
        feature_names.append(f'max_consecutive_{base}')
    for dinuc in dinuc_names:
        feature_names.append(f'dinuc_freq_{dinuc}')

    for i, seq in enumerate(sequences):
        if i % 1000 == 0 and i > 0:
            print(f"    已处理 {i}/{len(sequences)} 个序列")

        # 验证和标准化序列
        normalized_seq = validate_and_normalize_sequence(seq, seq_length)

        seq_features = []

        # 1. 碱基组成频率 (5维)
        base_counts = Counter(normalized_seq)
        total_bases = len(normalized_seq)

        for base in base_names:
            freq = base_counts.get(base, 0) / total_bases
            seq_features.append(float(freq))

        # 2. GC含量 (1维)
        gc_count = base_counts.get('G', 0) + base_counts.get('C', 0)
        gc_content = gc_count / total_bases
        seq_features.append(float(gc_content))

        # 3. 最大连续碱基长度 (4维，不包括N)
        for base in base_names[:4]:
            max_consecutive = 0
            current_consecutive = 0

            for nucleotide in normalized_seq:
                if nucleotide == base:
                    current_consecutive += 1
                    max_consecutive = max(max_consecutive, current_consecutive)
                else:
                    current_consecutive = 0

            # 归一化到[0,1]
            normalized_consecutive = max_consecutive / seq_length
            seq_features.append(float(normalized_consecutive))

        # 4. 二核苷酸频率 (10维)
        # 计算所有可能的二核苷酸
        dinuc_counts = {}
        for j in range(len(normalized_seq) - 1):
            dinuc = normalized_seq[j:j+2]
            if 'N' not in dinuc:  # 排除包含N的二核苷酸
                dinuc_counts[dinuc] = dinuc_counts.get(dinuc, 0) + 1

        total_dinucs = sum(dinuc_counts.values())

        for dinuc in dinuc_names:
            if total_dinucs > 0:
                freq = dinuc_counts.get(dinuc, 0) / total_dinucs
            else:
                freq = 0.0
            seq_features.append(float(freq))

        # 验证特征向量长度
        expected_length = len(feature_names)
        if len(seq_features) != expected_length:
            raise ValueError(f"特征向量长度不匹配: 期望{expected_length}, 实际{len(seq_features)}")

        features.append(seq_features)

    features_array = np.array(features, dtype=np.float64)

    # 数据质量检查
    print(f"  ✓ 特征提取完成: {features_array.shape}")
    print(f"  特征统计: min={features_array.min():.6f}, max={features_array.max():.6f}")

    # 检查NaN值
    nan_count = np.isnan(features_array).sum()
    if nan_count > 0:
        print(f"  ⚠️  发现 {nan_count} 个NaN值，将替换为0")
        features_array = np.nan_to_num(features_array, nan=0.0)

    return features_array, feature_names

def create_mutation_id_from_features(feature_data):
    """从特征数据创建mutation_id用于对齐"""
    mutation_ids = []

    # 尝试不同的列名组合来构建ID
    possible_id_columns = [
        ['#chr', 'pos', 'ref', 'alt'],
        ['chr', 'pos', 'ref', 'alt'],
        ['chrom', 'pos', 'ref', 'alt'],
        ['chromosome', 'position', 'reference', 'alternative']
    ]

    id_columns = None
    for cols in possible_id_columns:
        if all(col in feature_data.columns for col in cols):
            id_columns = cols
            break

    if id_columns:
        print(f"  使用列 {id_columns} 构建mutation_id")
        for _, row in feature_data.iterrows():
            chrom = str(row[id_columns[0]]).replace('chr', '')
            pos = str(row[id_columns[1]])
            ref = str(row[id_columns[2]])
            alt = str(row[id_columns[3]])
            mutation_id = f"{chrom}:{pos}:{ref}>{alt}"
            mutation_ids.append(mutation_id)
    else:
        print("  ⚠️  无法从特征数据构建mutation_id，使用行索引")
        mutation_ids = [f"row_{i}" for i in range(len(feature_data))]

    return mutation_ids

def align_features_and_sequences(feature_data, seq_df, feat_names):
    """对齐特征数据和序列数据"""
    print("  开始特征-序列对齐...")

    # 为特征数据创建mutation_id
    feature_mutation_ids = create_mutation_id_from_features(feature_data)
    feature_data_with_id = feature_data.copy()
    feature_data_with_id['mutation_id'] = feature_mutation_ids

    # 检查序列数据是否有mutation_id
    if 'mutation_id' not in seq_df.columns:
        print("  ⚠️  序列数据缺少mutation_id，使用位置对齐")
        # 简单的位置对齐
        min_len = min(len(feature_data_with_id), len(seq_df))
        aligned_features = feature_data_with_id.iloc[:min_len]
        aligned_sequences = seq_df.iloc[:min_len]

        print(f"  位置对齐完成: {min_len} 个样本")
        return aligned_features, aligned_sequences

    # 基于mutation_id进行精确对齐
    print(f"  特征数据: {len(feature_data_with_id)} 个样本")
    print(f"  序列数据: {len(seq_df)} 个样本")

    # 内连接对齐
    aligned_data = pd.merge(
        feature_data_with_id,
        seq_df,
        on='mutation_id',
        how='inner',
        suffixes=('_feat', '_seq')
    )

    if len(aligned_data) == 0:
        print("  ✗ 基于ID的对齐失败，回退到位置对齐")
        min_len = min(len(feature_data_with_id), len(seq_df))
        aligned_features = feature_data_with_id.iloc[:min_len]
        aligned_sequences = seq_df.iloc[:min_len]
        print(f"  位置对齐完成: {min_len} 个样本")
        return aligned_features, aligned_sequences

    print(f"  ✓ ID对齐成功: {len(aligned_data)} 个样本")

    # 检查标签一致性
    if 'label' in aligned_data.columns and '#class' in aligned_data.columns:
        label_mismatch = (aligned_data['label'] != aligned_data['#class']).sum()
        if label_mismatch > 0:
            print(f"  ⚠️  发现 {label_mismatch} 个标签不匹配的样本")

    # 分离特征和序列数据
    feature_columns = list(feature_data.columns) + ['mutation_id']
    sequence_columns = list(seq_df.columns)

    aligned_features = aligned_data[feature_columns]
    aligned_sequences = aligned_data[sequence_columns]

    return aligned_features, aligned_sequences

def prepare_dataset(feature_file, seq_folder, feat_names):
    """准备单个数据集，确保特征-序列对齐"""
    print(f"\n--- 准备数据集 ---")
    print(f"特征文件: {feature_file}")
    print(f"序列文件夹: {seq_folder}")

    # 1. 加载传统特征
    if not os.path.exists(feature_file):
        print(f"✗ 特征文件不存在: {feature_file}")
        return None

    feature_data, _ = rename_with_feat40(feature_file)
    print(f"✓ 传统特征数据形状: {feature_data.shape}")

    # 2. 加载DNA序列
    if not os.path.exists(seq_folder):
        print(f"✗ 序列文件夹不存在: {seq_folder}")
        return None

    seq_df = load_dna_sequences_from_folder(seq_folder)
    if seq_df.empty:
        print("✗ 序列加载失败")
        return None

    print(f"✓ 序列数量: {len(seq_df)}")

    # 3. 特征-序列对齐
    aligned_features, aligned_sequences = align_features_and_sequences(
        feature_data, seq_df, feat_names
    )

    # 4. 获取对齐后的标签
    if '#class' in aligned_features.columns:
        true_labels = aligned_features['#class'].values
    elif 'label' in aligned_sequences.columns:
        true_labels = aligned_sequences['label'].values
    else:
        print("✗ 无法找到标签列")
        return None

    print(f"✓ 对齐后样本数: {len(aligned_features)}")
    print(f"✓ 标签分布: 正样本={sum(true_labels)}, 负样本={len(true_labels)-sum(true_labels)}")

    # 5. 序列特征提取
    sequences = aligned_sequences['sequence'].tolist()
    seq_features, seq_feature_names = extract_sequence_features_robust(sequences)
    print(f"✓ 序列特征形状: {seq_features.shape}")

    # 6. 处理传统特征
    X_traditional = aligned_features[feat_names]
    X_traditional.replace('na', np.nan, inplace=True)
    X_traditional = X_traditional.astype(float, errors='ignore')
    print(f"✓ 传统特征形状: {X_traditional.shape}")

    # 7. 组合特征
    seq_df_features = pd.DataFrame(
        seq_features,
        columns=seq_feature_names,
        index=X_traditional.index
    )
    combined_features = pd.concat([X_traditional, seq_df_features], axis=1)
    print(f"✓ 组合特征形状: {combined_features.shape}")

    # 8. 数据质量检查
    print("  数据质量检查:")
    print(f"    缺失值: {combined_features.isnull().sum().sum()}")
    print(f"    无穷值: {np.isinf(combined_features.select_dtypes(include=[np.number])).sum().sum()}")

    return {
        'features': combined_features,
        'labels': true_labels.astype(int),
        'n_samples': len(aligned_features),
        'n_positive': int(sum(true_labels)),
        'n_negative': int(len(true_labels) - sum(true_labels)),
        'traditional_features': len(feat_names),
        'sequence_features': len(seq_feature_names),
        'total_features': combined_features.shape[1],
        'feature_names': {
            'traditional': feat_names,
            'sequence': seq_feature_names,
            'all': combined_features.columns.tolist()
        }
    }

def optimize_hyperparameters(X_train, y_train, cv_folds=5):
    """超参数优化"""
    print("  开始超参数优化...")

    # 定义参数网格
    param_grid = {
        'n_estimators': [100, 200],
        'learning_rate': [0.05, 0.1, 0.15],
        'max_depth': [4, 6, 8],
        'subsample': [0.8, 0.9, 1.0],
        'colsample_bytree': [0.8, 0.9, 1.0]
    }

    # 基础模型
    base_model = XGBClassifier(
        random_state=42,
        n_jobs=-1,
        eval_metric='logloss'
    )

    # 网格搜索
    grid_search = GridSearchCV(
        estimator=base_model,
        param_grid=param_grid,
        cv=StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42),
        scoring='roc_auc',
        n_jobs=-1,
        verbose=1
    )

    grid_search.fit(X_train, y_train)

    print(f"  ✓ 最佳参数: {grid_search.best_params_}")
    print(f"  ✓ 最佳CV AUC: {grid_search.best_score_:.4f}")

    return grid_search.best_estimator_, grid_search.best_params_

def train_and_evaluate_model(train_data, test_datasets, use_cv=True, optimize_params=True):
    """训练模型并在所有数据集上评估"""
    print(f"\n=== 开始训练和评估 ===")

    # 1. 准备训练数据
    print("1. 准备训练数据...")
    X_train_full = train_data['features']
    y_train_full = train_data['labels']

    # 检查数据平衡性
    label_counts = Counter(y_train_full)
    print(f"  标签分布: {dict(label_counts)}")

    # 数据预处理
    print("  数据预处理...")
    imputer = SimpleImputer(strategy='median')
    X_train_imputed = imputer.fit_transform(X_train_full)

    scaler = MinMaxScaler()
    X_train_scaled = scaler.fit_transform(X_train_imputed)

    print(f"✓ 训练数据预处理完成: {X_train_scaled.shape}")

    # 2. 模型训练
    if optimize_params:
        print("2. 超参数优化和模型训练...")
        model, best_params = optimize_hyperparameters(X_train_scaled, y_train_full)
    else:
        print("2. 使用默认参数训练模型...")
        model = XGBClassifier(
            n_estimators=200,
            learning_rate=0.1,
            max_depth=6,
            subsample=0.9,
            colsample_bytree=0.9,
            random_state=42,
            n_jobs=-1,
            eval_metric='logloss'
        )
        model.fit(X_train_scaled, y_train_full)
        best_params = model.get_params()

    print("✓ 模型训练完成")

    # 3. 交叉验证评估
    if use_cv:
        print("3. 交叉验证评估...")
        cv_scores = []
        skf = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)

        for fold, (train_idx, val_idx) in enumerate(skf.split(X_train_scaled, y_train_full)):
            X_fold_train, X_fold_val = X_train_scaled[train_idx], X_train_scaled[val_idx]
            y_fold_train, y_fold_val = y_train_full[train_idx], y_train_full[val_idx]

            fold_model = XGBClassifier(**best_params)
            fold_model.fit(X_fold_train, y_fold_train)

            val_pred = fold_model.predict_proba(X_fold_val)[:, 1]
            fold_auc = roc_auc_score(y_fold_val, val_pred)
            cv_scores.append(fold_auc)

            print(f"  Fold {fold+1} AUC: {fold_auc:.4f}")

        cv_mean = np.mean(cv_scores)
        cv_std = np.std(cv_scores)
        print(f"✓ 交叉验证 AUC: {cv_mean:.4f} ± {cv_std:.4f}")
    else:
        cv_mean = cv_std = None

    # 4. 评估所有数据集
    results = []

    # 评估训练集
    print("\n4. 评估训练集...")
    train_pred = model.predict_proba(X_train_scaled)[:, 1]
    train_metrics = metricsScores(y_train_full, train_pred)

    train_result = {
        'dataset': '训练集',
        'n_samples': int(train_data['n_samples']),
        'n_positive': int(train_data['n_positive']),
        'n_negative': int(train_data['n_negative']),
        'metrics': {k: float(v) for k, v in zip(['Sen', 'Spe', 'Pre', 'F1', 'MCC', 'ACC', 'AUC', 'AUPR'], train_metrics[:8])},
        'cv_auc_mean': float(cv_mean) if cv_mean is not None else None,
        'cv_auc_std': float(cv_std) if cv_std is not None else None,
        'best_params': best_params
    }
    results.append(train_result)

    print("训练集结果:")
    for name, value in train_result['metrics'].items():
        print(f"  {name}: {value:.4f}")
    if cv_mean is not None:
        print(f"  CV AUC: {cv_mean:.4f} ± {cv_std:.4f}")

    # 评估测试集
    for test_name, test_data in test_datasets.items():
        print(f"\n5. 评估 {test_name}...")

        # 预处理测试数据
        X_test = test_data['features']
        y_test = test_data['labels']

        print(f"  测试数据形状: {X_test.shape}")
        print(f"  特征匹配检查...")

        # 确保特征列一致
        if list(X_test.columns) != list(X_train_full.columns):
            print("  ⚠️  特征列不完全匹配，进行对齐...")
            common_features = list(set(X_test.columns) & set(X_train_full.columns))
            print(f"  共同特征数: {len(common_features)}")
            X_test = X_test[common_features]
            X_train_full_aligned = X_train_full[common_features]

            # 重新训练预处理器
            imputer_aligned = SimpleImputer(strategy='median')
            scaler_aligned = MinMaxScaler()
            X_train_aligned = imputer_aligned.fit_transform(X_train_full_aligned)
            X_train_aligned = scaler_aligned.fit_transform(X_train_aligned)

            X_test_imputed = imputer_aligned.transform(X_test)
            X_test_scaled = scaler_aligned.transform(X_test_imputed)
        else:
            X_test_imputed = imputer.transform(X_test)
            X_test_scaled = scaler.transform(X_test_imputed)

        print(f"  预处理后测试数据形状: {X_test_scaled.shape}")

        # 预测
        test_pred = model.predict_proba(X_test_scaled)[:, 1]
        test_metrics = metricsScores(y_test, test_pred)

        test_result = {
            'dataset': test_name,
            'n_samples': int(test_data['n_samples']),
            'n_positive': int(test_data['n_positive']),
            'n_negative': int(test_data['n_negative']),
            'metrics': {k: float(v) for k, v in zip(['Sen', 'Spe', 'Pre', 'F1', 'MCC', 'ACC', 'AUC', 'AUPR'], test_metrics[:8])},
            'feature_alignment': 'aligned' if list(X_test.columns) != list(X_train_full.columns) else 'matched'
        }
        results.append(test_result)

        print(f"{test_name}结果:")
        for name, value in test_result['metrics'].items():
            print(f"  {name}: {value:.4f}")

    return results

def save_results(results):
    """保存结果"""
    print(f"\n=== 保存结果 ===")
    
    # 创建结果目录
    os.makedirs('./results', exist_ok=True)
    
    # 保存详细结果
    with open('./results/integrated_evaluation_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    # 保存CSV摘要
    summary_data = []
    for result in results:
        row = {
            'dataset': result['dataset'],
            'n_samples': result['n_samples'],
            'n_positive': result['n_positive'],
            'n_negative': result['n_negative']
        }
        row.update(result['metrics'])
        summary_data.append(row)
    
    df_summary = pd.DataFrame(summary_data)
    df_summary.to_csv('./results/integrated_evaluation_summary.csv', index=False)
    
    print("✓ 结果已保存到:")
    print("  - ./results/integrated_evaluation_results.json")
    print("  - ./results/integrated_evaluation_summary.csv")

def main():
    """主函数"""
    print("🧬 修复版训练+评估流程")
    print("解决ID对齐、数据类型、交叉验证等关键问题")
    print("=" * 60)

    # 配置参数
    USE_CROSS_VALIDATION = True
    OPTIMIZE_HYPERPARAMETERS = False  # 设为False以加快运行，True以获得最佳性能

    print(f"配置: CV={USE_CROSS_VALIDATION}, 超参数优化={OPTIMIZE_HYPERPARAMETERS}")

    # 1. 加载特征名称
    print("\n1. 加载特征配置...")
    try:
        with open('./data/feat146.json', 'r', encoding='utf-8') as f:
            feat146 = json.load(f)
        print(f"✓ 特征名称数量: {len(feat146)}")
    except Exception as e:
        print(f"✗ 加载特征名称失败: {e}")
        return 1

    # 2. 定义数据集配置
    datasets_config = {
        'train': {
            'feature_file': './data/feature-encoded/for-train-test/train-closeby6-encoded-feat146.csv',
            'seq_folder': './data/train_seq_cosmic'
        },
        'test1': {
            'feature_file': './data/feature-encoded/for-train-test/test1-closeby6-encoded-feat146.csv',
            'seq_folder': './data/test1_seq_cosmic'
        },
        'test2': {
            'feature_file': './data/feature-encoded/for-train-test/test2-closeby6-encoded-feat146.csv',
            'seq_folder': './data/test2_seq_cosmic'
        }
    }

    # 3. 数据集存在性检查
    print("\n2. 检查数据文件...")
    missing_files = []
    for name, config in datasets_config.items():
        if not os.path.exists(config['feature_file']):
            missing_files.append(f"{name}: {config['feature_file']}")
        if not os.path.exists(config['seq_folder']):
            missing_files.append(f"{name}: {config['seq_folder']}")

    if missing_files:
        print("✗ 缺少以下文件/文件夹:")
        for f in missing_files:
            print(f"  - {f}")
        return 1

    print("✓ 所有数据文件存在")

    # 4. 准备所有数据集
    print("\n3. 准备所有数据集...")

    train_data = prepare_dataset(
        datasets_config['train']['feature_file'],
        datasets_config['train']['seq_folder'],
        feat146
    )

    if train_data is None:
        print("✗ 训练数据准备失败")
        return 1

    test_datasets = {}
    for test_name in ['test1', 'test2']:
        print(f"\n准备{test_name}...")
        test_data = prepare_dataset(
            datasets_config[test_name]['feature_file'],
            datasets_config[test_name]['seq_folder'],
            feat146
        )
        if test_data is not None:
            test_datasets[f'测试集{test_name[-1]}'] = test_data
        else:
            print(f"⚠️  {test_name}准备失败，将跳过")

    if not test_datasets:
        print("✗ 没有可用的测试数据集")
        return 1

    print(f"\n✓ 成功准备 {len(test_datasets)} 个测试数据集")

    # 5. 训练和评估
    print("\n4. 开始训练和评估...")
    results = train_and_evaluate_model(
        train_data,
        test_datasets,
        use_cv=USE_CROSS_VALIDATION,
        optimize_params=OPTIMIZE_HYPERPARAMETERS
    )

    # 6. 保存结果
    print("\n5. 保存结果...")
    save_results(results)

    # 7. 显示详细汇总
    print(f"\n=== 最终结果汇总 ===")
    print("=" * 90)
    print(f"{'数据集':<10} {'样本数':<8} {'正/负':<10} {'AUC':<8} {'AUPR':<8} {'ACC':<8} {'F1':<8} {'MCC':<8}")
    print("-" * 90)

    for result in results:
        pos_neg = f"{result['n_positive']}/{result['n_negative']}"
        print(f"{result['dataset']:<10} "
              f"{result['n_samples']:<8} "
              f"{pos_neg:<10} "
              f"{result['metrics']['AUC']:<8.4f} "
              f"{result['metrics']['AUPR']:<8.4f} "
              f"{result['metrics']['ACC']:<8.4f} "
              f"{result['metrics']['F1']:<8.4f} "
              f"{result['metrics']['MCC']:<8.4f}")

    # 8. 显示关键改进信息
    print(f"\n=== 关键改进总结 ===")
    print("✅ ID对齐检查: 确保特征与序列正确匹配")
    print("✅ 数据类型修复: 正确处理二核苷酸计数和数值特征")
    print("✅ 序列验证: 验证DNA序列有效性并标准化")
    print("✅ 特征质量检查: 检测和处理NaN值、无穷值")
    if USE_CROSS_VALIDATION:
        print("✅ 交叉验证: 5折交叉验证评估模型稳定性")
    if OPTIMIZE_HYPERPARAMETERS:
        print("✅ 超参数优化: 网格搜索最佳参数组合")

    # 9. 数据质量报告
    train_result = results[0]
    if 'cv_auc_mean' in train_result and train_result['cv_auc_mean'] is not None:
        print(f"\n交叉验证结果: AUC = {train_result['cv_auc_mean']:.4f} ± {train_result['cv_auc_std']:.4f}")

    print(f"\n🎉 修复版评估完成!")
    print("查看 ./results/ 目录获取详细结果")
    return 0

if __name__ == "__main__":
    exit(main())
