import pandas as pd
import numpy as np
import joblib, json
from utils import rename_with_feat40, metricsScores
from sklearn.model_selection import cross_val_predict
from sklearn.preprocessing import MinMaxScaler
from sklearn.impute import SimpleImputer
import warnings
warnings.filterwarnings("ignore")

# 加载特征和数据
feat146_cols = pd.read_csv(r'./results/AAAA_featureSelect/feat_importance_sorted_rename.csv')
feat73_cols = feat146_cols['feat_name'][:73]

with open('./data/feat146.json', 'r') as f:
    feat146 = json.load(f)

# 读取训练数据
print("处理训练集数据...")
X_train, renamed_columns = rename_with_feat40(r'./data/feature-encoded/for-train-test/train-closeby6-encoded-feat146.csv')
print(f"训练集数据形状: {X_train.shape}")

# 数据预处理
X_features = X_train[feat146].copy()
X_features.replace('na', np.nan, inplace=True)
y_true = X_train['#class'].values

# 重新训练预处理器和模型进行交叉验证
imputer = SimpleImputer(strategy='median')
minmax = MinMaxScaler()

# 预处理
X_imputed = imputer.fit_transform(X_features)
X_scaled = minmax.fit_transform(X_imputed)
X_final = pd.DataFrame(X_scaled, columns=feat146)

# 选择73个特征
X_selected = X_final[feat73_cols]

# 加载模型架构（不是训练好的权重）
from sklearn.ensemble import VotingClassifier
from xgboost import XGBClassifier
from sklearn.ensemble import RandomForestClassifier

# 重新定义模型（使用相同架构但重新训练）
model = VotingClassifier([
    ('xgb', XGBClassifier(random_state=42)),
    ('rf', RandomForestClassifier(random_state=42))
], voting='soft')

# 使用交叉验证获得无偏预测
pred_train = cross_val_predict(model, X_selected.values, y_true, 
                              cv=5, method='predict_proba')[:, 1]

# 计算评估指标
metrics = 'Sen, Spe, Pre, F1, MCC, ACC, AUC, AUPR, tn, fp, fn, tp, thres'.split(', ')
result_df_train = pd.concat([
    pd.Series(metrics), 
    pd.Series(metricsScores(y_true, pred_train))
], axis=1).T

result_df_train.columns = result_df_train.iloc[0]
result_df_train = result_df_train[1:]


print(result_df_train.iloc[:, :-5])