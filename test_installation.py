#!/usr/bin/env python3
"""
安装和组件测试脚本
验证CNN+XGBoost集成模型的所有组件是否正常工作
"""

import sys
import os
import traceback
import warnings
warnings.filterwarnings("ignore")

def test_imports():
    """测试所有必要的包导入"""
    print("=== 测试包导入 ===")
    
    packages = [
        ('torch', 'PyTorch深度学习框架'),
        ('numpy', '数值计算'),
        ('pandas', '数据处理'),
        ('sklearn', 'scikit-learn机器学习'),
        ('xgboost', 'XGBoost梯度提升'),
        ('matplotlib', '绘图库'),
        ('seaborn', '统计可视化'),
        ('joblib', '模型序列化')
    ]
    
    failed_imports = []
    
    for package, description in packages:
        try:
            if package == 'sklearn':
                import sklearn
            else:
                __import__(package)
            print(f"✓ {package} - {description}")
        except ImportError as e:
            print(f"✗ {package} - {description} (错误: {e})")
            failed_imports.append(package)
    
    if failed_imports:
        print(f"\n缺少包: {', '.join(failed_imports)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("所有包导入成功!")
    return True

def test_data_files():
    """测试数据文件是否存在"""
    print("\n=== 测试数据文件 ===")
    
    files = [
        ('./data/feat146.json', '146维特征名称配置'),
        ('./data/seq_cosmic/train_pos_seq.txt', '正样本DNA序列'),
        ('./data/seq_cosmic/train_neg_seq.txt', '负样本DNA序列'),
        ('./data/feature-encoded/for-train-test/train-closeby6-encoded-feat146.csv', '训练特征数据')
    ]
    
    missing_files = []
    
    for file_path, description in files:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"✓ {file_path} - {description} ({size} bytes)")
        else:
            print(f"✗ {file_path} - {description}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n缺少文件: {len(missing_files)} 个")
        return False
    
    print("所有数据文件存在!")
    return True

def test_cnn_module():
    """测试CNN模块"""
    print("\n=== 测试CNN模块 ===")
    
    try:
        from code.dna_cnn_module import DNASequenceDataset, DNACNNModel, DNAFeatureExtractor
        
        # 测试数据集类
        test_sequences = ['ATCGATCGATCGATCG' * 8]  # 128bp
        dataset = DNASequenceDataset(test_sequences)
        print(f"✓ DNASequenceDataset - 创建成功")
        
        # 测试数据编码
        encoded = dataset.encode_sequence(test_sequences[0])
        print(f"✓ 序列编码 - 形状: {encoded.shape}")
        
        # 测试CNN模型
        model = DNACNNModel(seq_length=128, feature_dim=64)
        print(f"✓ DNACNNModel - 创建成功")
        
        # 测试特征提取器
        extractor = DNAFeatureExtractor()
        print(f"✓ DNAFeatureExtractor - 创建成功")
        
        # 测试特征提取
        features = extractor.extract_features(test_sequences)
        print(f"✓ 特征提取 - 输出形状: {features.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ CNN模块测试失败: {e}")
        traceback.print_exc()
        return False

def test_ensemble_module():
    """测试集成模块"""
    print("\n=== 测试集成模块 ===")
    
    try:
        from code.train_cnn_xgb_ensemble import CNNXGBEnsemble
        
        # 创建集成模型
        ensemble = CNNXGBEnsemble(cnn_feature_dim=64, use_gpu=False)
        print(f"✓ CNNXGBEnsemble - 创建成功")
        
        # 测试GPU检测
        import torch
        gpu_available = torch.cuda.is_available()
        print(f"✓ GPU可用性检测 - {'可用' if gpu_available else '不可用'}")
        
        return True
        
    except Exception as e:
        print(f"✗ 集成模块测试失败: {e}")
        traceback.print_exc()
        return False

def test_utils():
    """测试工具函数"""
    print("\n=== 测试工具函数 ===")
    
    try:
        from code.utils import metricsScores, rename_with_feat40
        
        # 测试评估指标计算
        import numpy as np
        y_true = np.array([0, 1, 1, 0, 1])
        y_pred = np.array([0.1, 0.8, 0.7, 0.2, 0.9])
        metrics = metricsScores(y_true, y_pred)
        print(f"✓ metricsScores - 计算成功, AUC: {metrics[6]:.3f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 工具函数测试失败: {e}")
        traceback.print_exc()
        return False

def test_data_loading():
    """测试数据加载"""
    print("\n=== 测试数据加载 ===")
    
    try:
        from code.dna_cnn_module import load_dna_sequences
        
        # 测试序列加载
        sequences, labels, metadata = load_dna_sequences(
            './data/seq_cosmic/train_pos_seq.txt',
            './data/seq_cosmic/train_neg_seq.txt'
        )
        
        print(f"✓ 序列加载成功 - {len(sequences)} 个序列")
        print(f"✓ 标签分布 - 正样本: {sum(labels)}, 负样本: {len(labels)-sum(labels)}")
        
        # 检查序列长度
        seq_lengths = [len(seq) for seq in sequences[:10]]
        print(f"✓ 序列长度示例: {seq_lengths}")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据加载测试失败: {e}")
        traceback.print_exc()
        return False

def test_feature_processing():
    """测试特征处理"""
    print("\n=== 测试特征处理 ===")
    
    try:
        import pandas as pd
        import json
        from code.utils import rename_with_feat40
        
        # 测试传统特征加载
        train_data, _ = rename_with_feat40(
            './data/feature-encoded/for-train-test/train-closeby6-encoded-feat146.csv'
        )
        print(f"✓ 传统特征加载 - 形状: {train_data.shape}")
        
        # 测试特征名称加载
        with open('./data/feat146.json', 'r') as f:
            feat146 = json.load(f)
        print(f"✓ 特征名称加载 - {len(feat146)} 个特征")
        
        return True
        
    except Exception as e:
        print(f"✗ 特征处理测试失败: {e}")
        traceback.print_exc()
        return False

def test_small_training():
    """测试小规模训练"""
    print("\n=== 测试小规模训练 ===")
    
    try:
        from code.train_cnn_xgb_ensemble import CNNXGBEnsemble
        from code.dna_cnn_module import load_dna_sequences
        from code.utils import rename_with_feat40
        import numpy as np
        
        # 加载少量数据
        sequences, labels, _ = load_dna_sequences(
            './data/seq_cosmic/train_pos_seq.txt',
            './data/seq_cosmic/train_neg_seq.txt'
        )
        
        train_data, _ = rename_with_feat40(
            './data/feature-encoded/for-train-test/train-closeby6-encoded-feat146.csv'
        )
        
        # 取少量样本
        n_samples = min(50, len(sequences), len(train_data))
        sequences = sequences[:n_samples]
        labels = labels[:n_samples]
        train_data = train_data.iloc[:n_samples]
        
        print(f"✓ 小规模数据准备 - {n_samples} 个样本")
        
        # 创建集成模型
        ensemble = CNNXGBEnsemble(cnn_feature_dim=32, use_gpu=False)
        
        # 测试特征提取
        cnn_features = ensemble.cnn_extractor.extract_features(sequences)
        print(f"✓ CNN特征提取 - 形状: {cnn_features.shape}")
        
        # 测试特征组合
        combined_features, _ = ensemble.prepare_features(train_data, sequences, labels)
        print(f"✓ 特征组合 - 形状: {combined_features.shape}")
        
        print("小规模训练测试成功!")
        return True
        
    except Exception as e:
        print(f"✗ 小规模训练测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧬 CNN+XGBoost集成模型安装测试")
    print("=" * 50)
    
    tests = [
        ("包导入", test_imports),
        ("数据文件", test_data_files),
        ("CNN模块", test_cnn_module),
        ("集成模块", test_ensemble_module),
        ("工具函数", test_utils),
        ("数据加载", test_data_loading),
        ("特征处理", test_feature_processing),
        ("小规模训练", test_small_training)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                failed += 1
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} 测试出错: {e}")
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        print("🎉 所有测试通过! 系统准备就绪")
        print("\n下一步:")
        print("1. 运行演示: python code/cnn_xgb_demo.py")
        print("2. 完整流程: python run_cnn_xgb_pipeline.py")
        return 0
    else:
        print("⚠️  部分测试失败，请检查错误信息并修复")
        return 1

if __name__ == "__main__":
    exit(main())
