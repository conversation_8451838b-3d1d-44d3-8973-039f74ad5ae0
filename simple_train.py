#!/usr/bin/env python3
"""
简化的训练脚本 - 使用现有的工具和数据进行训练
"""

import sys
import os
import pandas as pd
import numpy as np
import json

# 添加code目录到路径
sys.path.append('./code')
sys.path.append('.')

try:
    from utils import rename_with_feat40, metricsScores, fillMeanMaxMin2
    print("✓ 成功导入utils模块")
except ImportError as e:
    print(f"✗ 导入utils失败: {e}")
    sys.exit(1)

def load_dna_sequences_simple(pos_file, neg_file):
    """简单的DNA序列加载函数"""
    sequences = []
    labels = []
    
    print(f"加载正样本序列: {pos_file}")
    try:
        with open(pos_file, 'r') as f:
            for line in f:
                parts = line.strip().split('\t')
                if len(parts) >= 5:
                    seq = parts[4]
                    sequences.append(seq)
                    labels.append(1)
        print(f"✓ 加载了 {sum(labels)} 个正样本")
    except Exception as e:
        print(f"✗ 加载正样本失败: {e}")
        return [], []
    
    print(f"加载负样本序列: {neg_file}")
    try:
        with open(neg_file, 'r') as f:
            for line in f:
                parts = line.strip().split('\t')
                if len(parts) >= 5:
                    seq = parts[4]
                    sequences.append(seq)
                    labels.append(0)
        print(f"✓ 加载了 {len(labels) - sum(labels)} 个负样本")
    except Exception as e:
        print(f"✗ 加载负样本失败: {e}")
        return [], []
    
    return sequences, labels

def encode_dna_sequence_simple(sequence, seq_length=128):
    """简单的DNA序列编码函数"""
    # 确保序列长度为128bp
    if len(sequence) > seq_length:
        sequence = sequence[:seq_length]
    elif len(sequence) < seq_length:
        sequence = sequence + 'N' * (seq_length - len(sequence))
    
    # 简单的数值编码: A=0, T=1, G=2, C=3, N=4
    base_to_num = {'A': 0, 'T': 1, 'G': 2, 'C': 3, 'N': 4}
    encoded = []
    
    for base in sequence.upper():
        encoded.append(base_to_num.get(base, 4))
    
    return encoded

def extract_sequence_features_simple(sequences):
    """简单的序列特征提取"""
    print("提取序列特征...")
    
    features = []
    for seq in sequences:
        # 编码序列
        encoded = encode_dna_sequence_simple(seq)
        
        # 提取简单的统计特征
        seq_features = []
        
        # 碱基组成
        for base_num in range(5):  # A, T, G, C, N
            count = encoded.count(base_num)
            seq_features.append(count / len(encoded))
        
        # GC含量
        gc_count = encoded.count(2) + encoded.count(3)  # G + C
        seq_features.append(gc_count / len(encoded))
        
        # 连续碱基统计
        for base_num in range(4):
            max_consecutive = 0
            current_consecutive = 0
            for e in encoded:
                if e == base_num:
                    current_consecutive += 1
                    max_consecutive = max(max_consecutive, current_consecutive)
                else:
                    current_consecutive = 0
            seq_features.append(max_consecutive / len(encoded))
        
        # 二核苷酸频率 (简化版)
        dinucleotides = ['AA', 'AT', 'AG', 'AC', 'TT', 'TG', 'TC', 'GG', 'GC', 'CC']
        seq_str = ''.join([['A', 'T', 'G', 'C', 'N'][e] for e in encoded])
        for dinuc in dinucleotides:
            count = seq_str.count(dinuc)
            seq_features.append(count / max(1, len(seq_str) - 1))
        
        features.append(seq_features)
    
    return np.array(features)

def train_simple_model():
    """简化的模型训练"""
    print("=== 开始简化训练流程 ===")
    
    # 1. 加载传统特征数据
    print("1. 加载传统特征数据...")
    try:
        train_data, _ = rename_with_feat40('./data/feature-encoded/for-train-test/train-closeby6-encoded-feat146.csv')
        print(f"✓ 传统特征数据形状: {train_data.shape}")
    except Exception as e:
        print(f"✗ 加载传统特征失败: {e}")
        return
    
    # 2. 加载特征名称
    print("2. 加载特征名称...")
    try:
        with open('./data/feat146.json', 'r') as f:
            feat146 = json.load(f)
        print(f"✓ 特征名称数量: {len(feat146)}")
    except Exception as e:
        print(f"✗ 加载特征名称失败: {e}")
        return
    
    # 3. 加载DNA序列
    print("3. 加载DNA序列...")
    sequences, seq_labels = load_dna_sequences_simple(
        './data/seq_cosmic/train_pos_seq.txt',
        './data/seq_cosmic/train_neg_seq.txt'
    )
    
    if not sequences:
        print("✗ 序列加载失败")
        return
    
    print(f"✓ 序列数量: {len(sequences)}")
    print(f"✓ 标签分布: 正样本={sum(seq_labels)}, 负样本={len(seq_labels)-sum(seq_labels)}")
    
    # 4. 确保数据长度一致
    print("4. 数据对齐...")
    min_len = min(len(train_data), len(sequences))
    train_data = train_data.iloc[:min_len]
    sequences = sequences[:min_len]
    seq_labels = seq_labels[:min_len]
    
    print(f"✓ 对齐后样本数: {min_len}")
    
    # 5. 提取序列特征
    print("5. 提取序列特征...")
    seq_features = extract_sequence_features_simple(sequences)
    print(f"✓ 序列特征形状: {seq_features.shape}")
    
    # 6. 处理传统特征
    print("6. 处理传统特征...")
    X_traditional = train_data[feat146]
    X_traditional.replace('na', np.nan, inplace=True)
    X_traditional = X_traditional.astype(float, errors='ignore')
    print(f"✓ 传统特征形状: {X_traditional.shape}")
    
    # 7. 组合特征
    print("7. 组合特征...")
    seq_feature_names = [f'seq_feat_{i}' for i in range(seq_features.shape[1])]
    seq_df = pd.DataFrame(seq_features, columns=seq_feature_names, index=X_traditional.index)
    combined_features = pd.concat([X_traditional, seq_df], axis=1)
    print(f"✓ 组合特征形状: {combined_features.shape}")
    print(f"✓ 特征维度提升: {X_traditional.shape[1]} → {combined_features.shape[1]} (+{seq_features.shape[1]})")
    
    # 8. 数据预处理
    print("8. 数据预处理...")
    try:
        from sklearn.impute import SimpleImputer
        from sklearn.preprocessing import MinMaxScaler
        from sklearn.model_selection import train_test_split
        
        # 缺失值填充
        imputer = SimpleImputer(strategy='median')
        X_imputed = imputer.fit_transform(combined_features)
        
        # 归一化
        scaler = MinMaxScaler()
        X_scaled = scaler.fit_transform(X_imputed)
        
        print(f"✓ 预处理完成")
        
    except ImportError:
        print("✗ scikit-learn不可用，跳过预处理")
        X_scaled = combined_features.fillna(0).values
    
    # 9. 训练模型
    print("9. 训练模型...")
    try:
        from xgboost import XGBClassifier
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            X_scaled, seq_labels, test_size=0.2, random_state=42, stratify=seq_labels
        )
        
        # 训练XGBoost
        model = XGBClassifier(
            n_estimators=100,  # 减少树的数量以加快训练
            learning_rate=0.1,
            max_depth=6,
            random_state=42,
            n_jobs=-1
        )
        
        print("开始训练...")
        model.fit(X_train, y_train)
        print("✓ 训练完成")
        
        # 10. 评估模型
        print("10. 评估模型...")
        train_pred = model.predict_proba(X_train)[:, 1]
        test_pred = model.predict_proba(X_test)[:, 1]
        
        train_metrics = metricsScores(y_train, train_pred)
        test_metrics = metricsScores(y_test, test_pred)
        
        metrics_names = ['Sen', 'Spe', 'Pre', 'F1', 'MCC', 'ACC', 'AUC', 'AUPR']
        
        print("\n=== 训练结果 ===")
        print("训练集性能:")
        for name, value in zip(metrics_names, train_metrics[:8]):
            print(f"  {name}: {value:.4f}")
        
        print("\n测试集性能:")
        for name, value in zip(metrics_names, test_metrics[:8]):
            print(f"  {name}: {value:.4f}")
        
        # 11. 保存模型
        print("\n11. 保存模型...")
        try:
            import joblib
            os.makedirs('./model', exist_ok=True)
            
            joblib.dump(model, './model/simple_combined_model.pkl')
            joblib.dump(imputer, './model/simple_imputer.pkl')
            joblib.dump(scaler, './model/simple_scaler.pkl')
            
            # 保存特征名称
            feature_info = {
                'traditional_features': feat146,
                'sequence_features': seq_feature_names,
                'all_features': combined_features.columns.tolist(),
                'feature_counts': {
                    'traditional': len(feat146),
                    'sequence': len(seq_feature_names),
                    'total': len(combined_features.columns)
                }
            }
            
            with open('./model/simple_feature_info.json', 'w') as f:
                json.dump(feature_info, f, indent=2)
            
            # 保存训练报告
            training_report = {
                'model_type': 'Simple Combined Model (Traditional + Sequence)',
                'samples': {
                    'total': min_len,
                    'train': len(X_train),
                    'test': len(X_test)
                },
                'features': feature_info['feature_counts'],
                'performance': {
                    'train': dict(zip(metrics_names, train_metrics[:8])),
                    'test': dict(zip(metrics_names, test_metrics[:8]))
                }
            }
            
            with open('./model/simple_training_report.json', 'w') as f:
                json.dump(training_report, f, indent=2)
            
            print("✓ 模型和结果已保存到 ./model/ 目录")
            
        except Exception as e:
            print(f"✗ 保存模型失败: {e}")
        
        print("\n=== 训练完成 ===")
        return training_report
        
    except ImportError:
        print("✗ XGBoost不可用，无法训练模型")
        return None
    except Exception as e:
        print(f"✗ 训练失败: {e}")
        return None

def main():
    """主函数"""
    print("🧬 简化的序列+传统特征组合训练")
    print("=" * 50)
    
    # 检查数据文件
    required_files = [
        './data/feature-encoded/for-train-test/train-closeby6-encoded-feat146.csv',
        './data/feat146.json',
        './data/seq_cosmic/train_pos_seq.txt',
        './data/seq_cosmic/train_neg_seq.txt'
    ]
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    if missing_files:
        print("缺少以下文件:")
        for f in missing_files:
            print(f"  - {f}")
        return 1
    
    # 运行训练
    result = train_simple_model()
    
    if result:
        print("\n🎉 训练成功完成!")
        print("查看 ./model/ 目录获取保存的模型和结果")
        return 0
    else:
        print("\n❌ 训练失败")
        return 1

if __name__ == "__main__":
    exit(main())
