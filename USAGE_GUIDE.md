# 🧬 使用指南 - DNA序列+传统特征组合模型

## 📁 项目结构

```
M_seq-main/
├── train_and_evaluate.py          # 主要运行脚本
├── code/
│   ├── utils.py                   # 工具函数
│   ├── for_test.py               # 原始模型测试
│   └── dataprocess.py            # 数据处理
├── data/
│   ├── feat146.json              # 146维特征名称
│   ├── feature-encoded/          # 传统特征数据
│   ├── train_seq_cosmic/         # 训练集DNA序列
│   ├── test1_seq_cosmic/         # 测试集1 DNA序列
│   └── test2_seq_cosmic/         # 测试集2 DNA序列
├── results/                      # 结果输出目录
└── requirements.txt              # 依赖包列表
```

## 🚀 快速开始

### 1. 环境准备
```bash
# 安装依赖
pip install -r requirements.txt

# 如果numpy版本有问题
pip install "numpy<2"
```

### 2. 数据准备
确保您的数据文件结构如下：

#### DNA序列文件格式
```
# train_seq_cosmic/train_pos_seq.txt (正样本)
# train_seq_cosmic/train_neg_seq.txt (负样本)
chr20	37377139	C	T	CTGGGCGGACACTGCGCCGAGGGGCGGGGCTGGACGCGCGCTCCAAGATGGCGGCGAACGTGTTC...
```
- 列1: 染色体
- 列2: 位置
- 列3: 参考碱基
- 列4: 突变碱基
- 列5: 128bp DNA序列

#### 传统特征文件
- `train-closeby6-encoded-feat146.csv`: 训练集特征
- `test1-closeby6-encoded-feat146.csv`: 测试集1特征
- `test2-closeby6-encoded-feat146.csv`: 测试集2特征

### 3. 运行模型
```bash
# 一键运行训练和评估
python train_and_evaluate.py
```

## 📊 输出结果

### 控制台输出
```
🧬 整合训练+评估流程
============================================================
加载特征配置...
✓ 特征名称数量: 146

=== 准备所有数据集 ===
--- 准备数据集 ---
特征文件: ./data/feature-encoded/for-train-test/train-closeby6-encoded-feat146.csv
序列文件夹: ./data/train_seq_cosmic
✓ 传统特征数据形状: (3494, 147)
✓ 序列数量: 3494
✓ 组合特征形状: (3494, 166)

=== 开始训练和评估 ===
1. 准备训练数据...
✓ 训练数据预处理完成: (3494, 166)
2. 训练XGBoost模型...
✓ 模型训练完成

3. 评估训练集...
训练集结果:
  AUC: 0.9871
  AUPR: 0.9881
  ACC: 0.9659
  F1: 0.9660

4. 评估 测试集1...
测试集1结果:
  AUC: 0.8665
  AUPR: 0.8866
  ACC: 0.7983
  F1: 0.7944

=== 最终结果汇总 ===
数据集        样本数      正样本      负样本      AUC      AUPR     ACC      F1
训练集        3494     1747     1747     0.9871   0.9881   0.9659   0.9660
测试集1       6650     3325     3325     0.8665   0.8866   0.7983   0.7944
测试集2       1960     980      980      0.8403   0.8054   0.7668   0.8016
```

### 结果文件
- `./results/integrated_evaluation_summary.csv`: 结果摘要表格
- `./results/integrated_evaluation_results.json`: 详细结果JSON

## 🔧 模型详解

### 特征组成
- **传统特征**: 146维 (synMall注释特征)
- **序列特征**: 20维 (从128bp DNA序列提取)
  - 碱基组成 (5维): A, T, G, C, N比例
  - GC含量 (1维)
  - 连续碱基统计 (4维)
  - 二核苷酸频率 (10维)
- **总特征**: 166维

### 模型配置
```python
XGBClassifier(
    n_estimators=100,
    learning_rate=0.1,
    max_depth=6,
    random_state=42,
    n_jobs=-1
)
```

### 数据预处理
1. 缺失值填充 (中位数)
2. MinMax归一化 [0,1]

## 📈 性能指标说明

- **AUC**: ROC曲线下面积，越接近1越好
- **AUPR**: 精确率-召回率曲线下面积
- **ACC**: 准确率
- **F1**: F1分数，精确率和召回率的调和平均
- **MCC**: 马修斯相关系数
- **Sen**: 敏感性 (召回率)
- **Spe**: 特异性

## 🔍 常见问题

### Q1: 文件路径错误
```bash
# 确保在项目根目录运行
cd /path/to/M_seq-main
python train_and_evaluate.py
```

### Q2: 缺少依赖包
```bash
pip install numpy pandas scikit-learn xgboost
```

### Q3: 内存不足
- 减少XGBoost的n_estimators参数
- 使用较小的数据子集进行测试

### Q4: DNA序列文件格式错误
- 确保文件是制表符分隔
- 确保第5列包含128bp的DNA序列
- 检查文件编码是否为UTF-8

## 🎯 预期性能

基于我们的测试，预期性能范围：
- **训练集 AUC**: 0.98+ (接近完美)
- **测试集 AUC**: 0.84-0.87 (良好)
- **运行时间**: 5-15分钟
- **内存需求**: 2-4GB

## 🔄 自定义修改

### 修改模型参数
在 `train_and_evaluate.py` 中找到以下代码并修改：
```python
model = XGBClassifier(
    n_estimators=50,    # 减少树的数量加快训练
    learning_rate=0.05, # 降低学习率提高稳定性
    max_depth=4,        # 减少深度防止过拟合
    random_state=42,
    n_jobs=-1
)
```

### 修改序列特征
在 `extract_sequence_features_simple` 函数中添加新的特征提取逻辑。

### 添加新的评估指标
在结果保存部分添加更多评估指标的计算。

## 📞 技术支持

如果遇到问题：
1. 检查数据文件格式和路径
2. 确认Python环境和依赖包版本
3. 查看控制台错误信息
4. 检查生成的结果文件

---

🎉 **现在您可以直接运行 `python train_and_evaluate.py` 开始使用模型了！**
