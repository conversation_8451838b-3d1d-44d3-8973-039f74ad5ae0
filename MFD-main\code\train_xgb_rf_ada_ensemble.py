import pandas as pd
import numpy as np
import json
import joblib
from pathlib import Path
from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold
from sklearn.ensemble import VotingClassifier, RandomForestClassifier, AdaBoostClassifier
from sklearn.preprocessing import MinMaxScaler
from sklearn.impute import SimpleImputer
from sklearn.metrics import roc_auc_score, classification_report
from xgboost import XGBClassifier
import warnings
warnings.filterwarnings("ignore")

from utils import rename_with_feat40, metricsScores

class XGBRFAdaEnsemble:
    """XGBoost + 随机森林 + AdaBoost 集成模型"""
    
    def __init__(self, use_gpu=True):
        self.use_gpu = use_gpu
        self.xgb_model = None
        self.rf_model = None
        self.ada_model = None
        self.ensemble_model = None
        self.imputer = None
        self.scaler = None
        self.feature_names = None
        self.is_fitted = False
        
    def _init_models(self):
        """初始化三个基础模型"""
        # XGBoost模型
        if self.use_gpu:
            self.xgb_model = XGBClassifier(
                n_estimators=200,
                learning_rate=0.1,
                max_depth=6,
                tree_method='gpu_hist',
                gpu_id=0,
                random_state=42,
                n_jobs=1,
                eval_metric='logloss'
            )
        else:
            self.xgb_model = XGBClassifier(
                n_estimators=200,
                learning_rate=0.1,
                max_depth=6,
                random_state=42,
                n_jobs=-1,
                eval_metric='logloss'
            )
        
        # 随机森林模型
        self.rf_model = RandomForestClassifier(
            n_estimators=200,
            max_depth=10,
            min_samples_split=5,
            min_samples_leaf=2,
            random_state=42,
            n_jobs=-1
        )
        
        # AdaBoost模型
        self.ada_model = AdaBoostClassifier(
            n_estimators=100,
            learning_rate=1.0,
            random_state=42
        )
        
        # 集成模型
        self.ensemble_model = VotingClassifier(
            estimators=[
                ('xgb', self.xgb_model),
                ('rf', self.rf_model),
                ('ada', self.ada_model)
            ],
            voting='soft',
            n_jobs=1 if self.use_gpu else -1
        )
    
    def _preprocess_data(self, X, y=None, fit=False):
        """数据预处理"""
        X_processed = X.copy()
        X_processed.replace('na', np.nan, inplace=True)
        X_processed = X_processed.astype(float, errors='ignore')
        
        if fit:
            # 训练时拟合预处理器
            self.imputer = SimpleImputer(strategy='median')
            self.scaler = MinMaxScaler()
            
            X_imputed = self.imputer.fit_transform(X_processed)
            X_scaled = self.scaler.fit_transform(X_imputed)
        else:
            # 预测时使用已拟合的预处理器
            X_imputed = self.imputer.transform(X_processed)
            X_scaled = self.scaler.transform(X_imputed)
        
        return X_scaled
    
    def fit(self, X, y, validation_split=0.2):
        """训练集成模型"""
        print("开始训练XGBoost+随机森林+AdaBoost集成模型...")
        
        # 保存特征名称
        self.feature_names = list(X.columns)
        
        # 初始化模型
        self._init_models()
        
        # 数据预处理
        X_processed = self._preprocess_data(X, y, fit=True)
        
        # 数据分割
        X_train, X_val, y_train, y_val = train_test_split(
            X_processed, y, test_size=validation_split, random_state=42, stratify=y
        )
        
        print(f"训练集大小: {X_train.shape}, 验证集大小: {X_val.shape}")
        print(f"训练集标签分布: {np.bincount(y_train)}")
        
        # 训练集成模型
        print("训练集成模型...")
        self.ensemble_model.fit(X_train, y_train)
        
        # 验证集评估
        val_proba = self.ensemble_model.predict_proba(X_val)[:, 1]
        val_auc = roc_auc_score(y_val, val_proba)
        
        # 单独评估各个模型
        xgb_proba = self.ensemble_model.named_estimators_['xgb'].predict_proba(X_val)[:, 1]
        rf_proba = self.ensemble_model.named_estimators_['rf'].predict_proba(X_val)[:, 1]
        ada_proba = self.ensemble_model.named_estimators_['ada'].predict_proba(X_val)[:, 1]
        
        xgb_auc = roc_auc_score(y_val, xgb_proba)
        rf_auc = roc_auc_score(y_val, rf_proba)
        ada_auc = roc_auc_score(y_val, ada_proba)
        
        print(f"XGBoost AUC: {xgb_auc:.4f}")
        print(f"随机森林 AUC: {rf_auc:.4f}")
        print(f"AdaBoost AUC: {ada_auc:.4f}")
        print(f"集成模型 AUC: {val_auc:.4f}")
        
        self.is_fitted = True
        
        return {
            'xgb_auc': xgb_auc,
            'rf_auc': rf_auc,
            'ada_auc': ada_auc,
            'ensemble_auc': val_auc
        }
    
    def predict_proba(self, X):
        """预测概率"""
        if not self.is_fitted:
            raise ValueError("模型尚未训练，请先调用fit()方法")
        
        X_processed = self._preprocess_data(X, fit=False)
        return self.ensemble_model.predict_proba(X_processed)
    
    def predict(self, X):
        """预测类别"""
        if not self.is_fitted:
            raise ValueError("模型尚未训练，请先调用fit()方法")
        
        X_processed = self._preprocess_data(X, fit=False)
        return self.ensemble_model.predict(X_processed)

def save_xgb_rf_ada_model(model, model_name, performance_metrics, feature_names):
    """保存XGBoost+随机森林+AdaBoost集成模型"""
    
    # 确保模型目录存在
    Path("model").mkdir(exist_ok=True)
    
    model_data = {
        # 集成模型
        'ensemble_model': model.ensemble_model,
        
        # 预处理器
        'imputer': model.imputer,
        'scaler': model.scaler,
        
        # 元数据
        'feature_names': feature_names,
        'use_gpu': model.use_gpu,
        'performance_metrics': performance_metrics,
        'model_type': 'XGBRFAdaEnsemble',
        
        # 版本信息
        'xgboost_version': XGBClassifier().__class__.__module__,
        'sklearn_version': __import__('sklearn').__version__
    }
    
    model_path = f"model/{model_name}.model"
    joblib.dump(model_data, model_path)
    
    # 单独保存预处理器 - 按要求命名
    imputer_path = './model/xg_rf_a_imputer.pkl'
    minmax_path = './model/xg_rf_a_minmax.pkl'
    
    joblib.dump(model.imputer, imputer_path)
    joblib.dump(model.scaler, minmax_path)
    
    absolute_path = Path(model_path).absolute()
    file_size = Path(model_path).stat().st_size / 1024
    
    print(f"✓ XGBoost+RF+AdaBoost集成模型已保存:")
    print(f"  主模型文件: {model_path}")
    print(f"  预处理器文件: {imputer_path}")
    print(f"  标准化器文件: {minmax_path}")
    print(f"  绝对路径: {absolute_path}")
    print(f"  文件大小: {file_size:.2f} KB")
    
    return model_path

def load_xgb_rf_ada_model(model_path):
    """加载XGBoost+随机森林+AdaBoost集成模型"""
    model_data = joblib.load(model_path)
    
    # 重建模型
    model = XGBRFAdaEnsemble(use_gpu=model_data['use_gpu'])
    model.ensemble_model = model_data['ensemble_model']
    model.imputer = model_data['imputer']
    model.scaler = model_data['scaler']
    model.feature_names = model_data['feature_names']
    model.is_fitted = True
    
    return model, model_data['performance_metrics']

def train_xgb_rf_ada_with_feat73(use_gpu=True):
    """使用73维最优特征训练XGBoost+RF+AdaBoost集成模型"""
    
    print("=" * 60)
    print("XGBoost+随机森林+AdaBoost集成模型训练 (73维最优特征)")
    print("=" * 60)
    
    # 1. 读取73维最优特征名称
    print("读取73维最优特征...")
    feat_file = './results/AAAA_featureSelect/feat_importance_sorted_rename.csv'
    
    if not Path(feat_file).exists():
        print(f"特征文件不存在: {feat_file}")
        feat73_cols = [f"feat_{i+1}" for i in range(73)]
        print("使用默认的73个特征名称")
    else:
        feat146_cols = pd.read_csv(feat_file)
        feat73_cols = feat146_cols['feat_name'][:73].tolist()
        print(f"成功读取73个最优特征")
    
    # 2. 读取146维特征名称映射
    with open('./data/feat146.json', 'r') as f:
        feat146 = json.load(f)
    
    # 3. 读取并重命名训练数据
    train_file = './data/feature-encoded/for-train-test/train-closeby6-encoded-feat146.csv'
    train_data, renamed_columns = rename_with_feat40(train_file)
    print(f"训练数据形状: {train_data.shape}")
    
    # 4. 确定标签列
    label_col = '#class'
    if label_col not in train_data.columns:
        print(f"标签列 {label_col} 不存在，可用列: {train_data.columns.tolist()}")
        return None, None, None
    
    # 5. 准备特征和标签
    X = train_data[feat73_cols]
    y = train_data[label_col]
    
    print(f"特征数据形状: {X.shape}")
    print(f"标签分布: {y.value_counts().to_dict()}")
    
    # 6. 训练集成模型
    model = XGBRFAdaEnsemble(use_gpu=use_gpu)
    performance = model.fit(X, y, validation_split=0.2)
    
    # 7. 交叉验证评估
    print("\n进行5折交叉验证...")
    X_processed = model._preprocess_data(X, fit=False)
    
    # 各模型交叉验证
    xgb_cv_scores = cross_val_score(model.ensemble_model.named_estimators_['xgb'], 
                                   X_processed, y, cv=5, scoring='roc_auc')
    rf_cv_scores = cross_val_score(model.ensemble_model.named_estimators_['rf'], 
                                  X_processed, y, cv=5, scoring='roc_auc')
    ada_cv_scores = cross_val_score(model.ensemble_model.named_estimators_['ada'], 
                                   X_processed, y, cv=5, scoring='roc_auc')
    ensemble_cv_scores = cross_val_score(model.ensemble_model, 
                                        X_processed, y, cv=5, scoring='roc_auc')
    
    print(f"XGBoost CV AUC: {xgb_cv_scores.mean():.4f} ± {xgb_cv_scores.std():.4f}")
    print(f"随机森林 CV AUC: {rf_cv_scores.mean():.4f} ± {rf_cv_scores.std():.4f}")
    print(f"AdaBoost CV AUC: {ada_cv_scores.mean():.4f} ± {ada_cv_scores.std():.4f}")
    print(f"集成模型 CV AUC: {ensemble_cv_scores.mean():.4f} ± {ensemble_cv_scores.std():.4f}")
    
    # 8. 保存性能指标
    final_performance = {
        **performance,
        'xgb_cv_auc_mean': xgb_cv_scores.mean(),
        'xgb_cv_auc_std': xgb_cv_scores.std(),
        'rf_cv_auc_mean': rf_cv_scores.mean(),
        'rf_cv_auc_std': rf_cv_scores.std(),
        'ada_cv_auc_mean': ada_cv_scores.mean(),
        'ada_cv_auc_std': ada_cv_scores.std(),
        'ensemble_cv_auc_mean': ensemble_cv_scores.mean(),
        'ensemble_cv_auc_std': ensemble_cv_scores.std(),
        'feature_count': len(feat73_cols),
        'training_samples': len(X)
    }
    
    # 9. 保存模型
    model_name = f"xgb_rf_ada_ensemble_feat73{'_gpu' if use_gpu else ''}"
    model_path = save_xgb_rf_ada_model(model, model_name, final_performance, feat73_cols)
    
    # 10. 保存特征名称
    with open('./model/xgb_rf_ada_feat73_names.json', 'w') as f:
        json.dump(feat73_cols, f, indent=2)
    
    # 11. 保存训练报告
    report = {
        'model_name': model_name,
        'model_path': model_path,
        'feature_count': len(feat73_cols),
        'training_samples': len(X),
        'performance_metrics': final_performance,
        'model_components': ['XGBoost', 'RandomForest', 'AdaBoost'],
        'use_gpu': use_gpu,
        'features_used': feat73_cols
    }
    
    with open('./model/xgb_rf_ada_training_report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n✅ 训练完成！")
    print(f"模型文件: {model_path}")
    print(f"预处理器文件: ./model/xg_rf_a_imputer.pkl")
    print(f"标准化器文件: ./model/xg_rf_a_minmax.pkl")
    print(f"特征文件: ./model/xgb_rf_ada_feat73_names.json")
    print(f"训练报告: ./model/xgb_rf_ada_training_report.json")
    print(f"最终集成AUC: {performance['ensemble_auc']:.4f}")
    print(f"实际使用特征数: {len(feat73_cols)}")
    
    return model, model_path, final_performance

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='训练XGBoost+随机森林+AdaBoost集成模型')
    parser.add_argument('--gpu', action='store_true', help='使用GPU加速XGBoost')
    parser.add_argument('--no-gpu', dest='gpu', action='store_false', help='使用CPU训练')
    parser.set_defaults(gpu=True)
    
    args = parser.parse_args()
    
    try:
        model, model_path, performance = train_xgb_rf_ada_with_feat73(use_gpu=args.gpu)
        print(f"\n🎉 训练成功完成！")
        print(f"模型已保存至: {model_path}")
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()