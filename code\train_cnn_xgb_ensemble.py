import pandas as pd
import numpy as np
import joblib
import json
import torch
from sklearn.ensemble import RandomForestClassifier
from xgboost import XGBClassifier
from sklearn.model_selection import train_test_split, StratifiedKFold
from sklearn.preprocessing import MinMaxScaler
from sklearn.impute import SimpleImputer
from sklearn.metrics import roc_auc_score, classification_report
from utils import rename_with_feat40, metricsScores
from dna_cnn_module import DNAFeatureExtractor, load_dna_sequences
import warnings
warnings.filterwarnings("ignore")

class CNNXGBEnsemble:
    """CNN+XGBoost集成模型"""
    
    def __init__(self, cnn_feature_dim=128, use_gpu=True):
        """
        初始化集成模型
        
        Args:
            cnn_feature_dim: CNN提取的特征维度
            use_gpu: 是否使用GPU
        """
        self.cnn_feature_dim = cnn_feature_dim
        self.use_gpu = use_gpu and torch.cuda.is_available()
        
        # 初始化CNN特征提取器
        device = torch.device('cuda' if self.use_gpu else 'cpu')
        self.cnn_extractor = DNAFeatureExtractor(device=device)
        
        # 初始化XGBoost模型
        if self.use_gpu:
            self.xgb_model = XGBClassifier(
                n_estimators=1000,
                learning_rate=0.1,
                max_depth=6,
                tree_method='gpu_hist',
                gpu_id=0,
                random_state=42,
                n_jobs=1
            )
        else:
            self.xgb_model = XGBClassifier(
                n_estimators=1000,
                learning_rate=0.1,
                max_depth=6,
                random_state=42,
                n_jobs=-1
            )
        
        # 数据预处理器
        self.imputer = SimpleImputer(strategy='median')
        self.scaler = MinMaxScaler()
        
        # 特征名称
        self.traditional_features = None
        self.cnn_feature_names = [f'cnn_feat_{i}' for i in range(cnn_feature_dim)]
        
    def load_traditional_features(self, train_file, feat_names_file):
        """
        加载传统特征数据
        
        Args:
            train_file: 训练数据文件路径
            feat_names_file: 特征名称文件路径
        """
        # 读取特征名称
        with open(feat_names_file, 'r') as f:
            self.traditional_features = json.load(f)
        
        # 读取并重命名训练数据
        train_data, _ = rename_with_feat40(train_file)
        
        return train_data
    
    def prepare_features(self, traditional_data, sequences, labels=None):
        """
        准备组合特征
        
        Args:
            traditional_data: 传统特征数据
            sequences: DNA序列列表
            labels: 标签（可选）
            
        Returns:
            combined_features: 组合特征
            labels: 标签
        """
        print("提取CNN特征...")
        # 提取CNN特征
        cnn_features = self.cnn_extractor.extract_features(sequences)
        print(f"CNN特征形状: {cnn_features.shape}")
        
        # 处理传统特征
        print("处理传统特征...")
        if self.traditional_features:
            X_traditional = traditional_data[self.traditional_features]
        else:
            # 如果没有指定特征，使用除标签外的所有特征
            label_cols = ['#class', 'label', 'class']
            feature_cols = [col for col in traditional_data.columns if col not in label_cols]
            X_traditional = traditional_data[feature_cols]
        
        # 处理缺失值
        X_traditional.replace('na', np.nan, inplace=True)
        X_traditional = X_traditional.astype(float, errors='ignore')
        
        # 确保样本数量一致
        min_samples = min(len(X_traditional), len(cnn_features))
        X_traditional = X_traditional.iloc[:min_samples]
        cnn_features = cnn_features[:min_samples]
        
        if labels is not None:
            labels = labels[:min_samples]
        
        print(f"传统特征形状: {X_traditional.shape}")
        print(f"CNN特征形状: {cnn_features.shape}")
        
        # 拼接特征
        cnn_df = pd.DataFrame(cnn_features, columns=self.cnn_feature_names, index=X_traditional.index)
        combined_features = pd.concat([X_traditional, cnn_df], axis=1)
        
        print(f"组合特征形状: {combined_features.shape}")
        
        return combined_features, labels
    
    def train(self, train_file, feat_names_file, pos_seq_file, neg_seq_file, 
              test_size=0.2, cnn_epochs=30):
        """
        训练集成模型
        
        Args:
            train_file: 训练数据文件
            feat_names_file: 特征名称文件
            pos_seq_file: 正样本序列文件
            neg_seq_file: 负样本序列文件
            test_size: 测试集比例
            cnn_epochs: CNN训练轮数
        """
        print("=== 开始训练CNN+XGBoost集成模型 ===")
        
        # 1. 加载数据
        print("1. 加载数据...")
        traditional_data = self.load_traditional_features(train_file, feat_names_file)
        sequences, seq_labels, metadata = load_dna_sequences(pos_seq_file, neg_seq_file)
        
        # 获取标签
        if '#class' in traditional_data.columns:
            labels = traditional_data['#class'].values
        elif 'label' in traditional_data.columns:
            labels = traditional_data['label'].values
        else:
            labels = np.array(seq_labels)
        
        print(f"数据加载完成: {len(sequences)} 个序列, {len(labels)} 个标签")
        print(f"标签分布: {np.bincount(labels)}")
        
        # 2. 训练CNN模型
        print("2. 训练CNN模型...")
        train_seq, val_seq, train_seq_labels, val_seq_labels = train_test_split(
            sequences, seq_labels, test_size=test_size, random_state=42, stratify=seq_labels
        )
        
        self.cnn_extractor.train_model(
            train_seq, train_seq_labels, 
            val_seq, val_seq_labels,
            epochs=cnn_epochs, batch_size=32
        )
        
        # 保存CNN模型
        self.cnn_extractor.save_model('./model/dna_cnn_model.pth')
        print("CNN模型训练完成并保存")
        
        # 3. 准备组合特征
        print("3. 准备组合特征...")
        combined_features, labels = self.prepare_features(traditional_data, sequences, labels)
        
        # 4. 数据预处理
        print("4. 数据预处理...")
        X_imputed = self.imputer.fit_transform(combined_features)
        X_scaled = self.scaler.fit_transform(X_imputed)
        X_final = pd.DataFrame(X_scaled, columns=combined_features.columns)
        
        # 5. 训练XGBoost
        print("5. 训练XGBoost模型...")
        X_train, X_test, y_train, y_test = train_test_split(
            X_final, labels, test_size=test_size, random_state=42, stratify=labels
        )
        
        self.xgb_model.fit(X_train, y_train)
        
        # 6. 评估模型
        print("6. 评估模型...")
        train_pred = self.xgb_model.predict_proba(X_train)[:, 1]
        test_pred = self.xgb_model.predict_proba(X_test)[:, 1]
        
        train_metrics = metricsScores(y_train, train_pred)
        test_metrics = metricsScores(y_test, test_pred)
        
        print("训练集性能:")
        metrics_names = ['Sen', 'Spe', 'Pre', 'F1', 'MCC', 'ACC', 'AUC', 'AUPR']
        for name, value in zip(metrics_names, train_metrics[:8]):
            print(f"  {name}: {value:.4f}")
        
        print("测试集性能:")
        for name, value in zip(metrics_names, test_metrics[:8]):
            print(f"  {name}: {value:.4f}")
        
        # 7. 保存模型和预处理器
        print("7. 保存模型...")
        joblib.dump(self.xgb_model, './model/cnn_xgb_ensemble_model.pkl')
        joblib.dump(self.imputer, './model/cnn_xgb_imputer.pkl')
        joblib.dump(self.scaler, './model/cnn_xgb_scaler.pkl')
        
        # 保存特征名称
        feature_info = {
            'traditional_features': self.traditional_features,
            'cnn_features': self.cnn_feature_names,
            'all_features': combined_features.columns.tolist()
        }
        with open('./model/cnn_xgb_feature_names.json', 'w') as f:
            json.dump(feature_info, f, indent=2)
        
        # 保存训练报告
        training_report = {
            'model_type': 'CNN+XGBoost Ensemble',
            'traditional_feature_dim': len(self.traditional_features) if self.traditional_features else X_traditional.shape[1],
            'cnn_feature_dim': self.cnn_feature_dim,
            'total_feature_dim': combined_features.shape[1],
            'train_samples': len(X_train),
            'test_samples': len(X_test),
            'train_metrics': dict(zip(metrics_names, train_metrics[:8])),
            'test_metrics': dict(zip(metrics_names, test_metrics[:8]))
        }
        
        with open('./model/cnn_xgb_training_report.json', 'w') as f:
            json.dump(training_report, f, indent=2)
        
        print("=== 训练完成 ===")
        return training_report
    
    def predict(self, traditional_data, sequences):
        """
        预测新样本
        
        Args:
            traditional_data: 传统特征数据
            sequences: DNA序列列表
            
        Returns:
            predictions: 预测概率
        """
        # 准备特征
        combined_features, _ = self.prepare_features(traditional_data, sequences)
        
        # 预处理
        X_imputed = self.imputer.transform(combined_features)
        X_scaled = self.scaler.transform(X_imputed)
        
        # 预测
        predictions = self.xgb_model.predict_proba(X_scaled)[:, 1]
        
        return predictions
    
    def load_trained_model(self, model_dir='./model'):
        """
        加载训练好的模型
        
        Args:
            model_dir: 模型目录
        """
        # 加载CNN模型
        cnn_model_path = f'{model_dir}/dna_cnn_model.pth'
        self.cnn_extractor.load_model(cnn_model_path)
        
        # 加载XGBoost模型
        self.xgb_model = joblib.load(f'{model_dir}/cnn_xgb_ensemble_model.pkl')
        self.imputer = joblib.load(f'{model_dir}/cnn_xgb_imputer.pkl')
        self.scaler = joblib.load(f'{model_dir}/cnn_xgb_scaler.pkl')
        
        # 加载特征名称
        with open(f'{model_dir}/cnn_xgb_feature_names.json', 'r') as f:
            feature_info = json.load(f)
            self.traditional_features = feature_info['traditional_features']
            self.cnn_feature_names = feature_info['cnn_features']

def main():
    """主函数"""
    # 初始化集成模型
    ensemble = CNNXGBEnsemble(cnn_feature_dim=128, use_gpu=True)
    
    # 训练模型
    training_report = ensemble.train(
        train_file='./data/feature-encoded/for-train-test/train-closeby6-encoded-feat146.csv',
        feat_names_file='./data/feat146.json',
        pos_seq_file='./data/seq_cosmic/train_pos_seq.txt',
        neg_seq_file='./data/seq_cosmic/train_neg_seq.txt',
        test_size=0.2,
        cnn_epochs=30
    )
    
    print("训练报告:")
    print(json.dumps(training_report, indent=2))

if __name__ == "__main__":
    main()
