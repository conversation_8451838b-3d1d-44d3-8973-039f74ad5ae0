import pandas as pd
import numpy as np
import joblib
import json
import os
from pathlib import Path
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.preprocessing import MinMaxScaler
from sklearn.impute import SimpleImputer
from sklearn.metrics import roc_auc_score, classification_report
import xgboost as xgb
import warnings
warnings.filterwarnings("ignore")

class SoftEnsembleXGBRF:
    """软集成XGBoost+随机森林模型 - PyTorch风格的融合思路"""
    
    def __init__(self, use_gpu=True):
        self.use_gpu = use_gpu
        
        # XGBoost模型配置
        self.xgb_params = {
            'objective': 'binary:logistic',
            'eval_metric': 'auc',
            'max_depth': 6,
            'learning_rate': 0.1,
            'n_estimators': 100,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'random_state': 42,
            'n_jobs': -1
        }
        
        if use_gpu:
            try:
                self.xgb_params.update({
                    'tree_method': 'gpu_hist',
                    'gpu_id': 0
                })
                print("✓ 启用GPU加速XGBoost")
            except:
                print("⚠ GPU不可用，使用CPU训练XGBoost")
                self.use_gpu = False
        
        # 随机森林模型配置
        self.rf_params = {
            'n_estimators': 100,
            'max_depth': 10,
            'min_samples_split': 5,
            'min_samples_leaf': 2,
            'random_state': 42,
            'n_jobs': -1
        }
        
        # 初始化模型
        self.xgb_model = xgb.XGBClassifier(**self.xgb_params)
        self.rf_model = RandomForestClassifier(**self.rf_params)
        
        # 预处理器
        self.imputer = SimpleImputer(strategy='median')
        self.scaler = MinMaxScaler()
        
        # 集成权重和其他属性
        self.ensemble_weights = [0.5, 0.5]
        self.feature_names = None
        self.is_fitted = False
    
    def _preprocess_data(self, X, fit=True):
        """数据预处理 - 参考PyTorch风格"""
        X_copy = X.copy()
        
        # 处理缺失值
        X_copy.replace('na', np.nan, inplace=True)
        X_copy = X_copy.astype(float, errors='ignore')
        
        if fit:
            X_processed = self.imputer.fit_transform(X_copy)
            X_processed = self.scaler.fit_transform(X_processed)
            self.feature_names = list(X.columns)
        else:
            X_processed = self.imputer.transform(X_copy)
            X_processed = self.scaler.transform(X_processed)
        
        return X_processed
    
    def fit(self, X, y, validation_split=0.2):
        """训练模型 - 使用PyTorch风格的训练流程"""
        print("开始训练软集成XGBoost+随机森林模型...")
        print(f"训练数据形状: {X.shape}")
        print(f"标签分布: {y.value_counts().to_dict()}")
        
        # 数据预处理
        X_processed = self._preprocess_data(X, fit=True)
        
        # 数据分割
        X_train, X_val, y_train, y_val = train_test_split(
            X_processed, y, test_size=validation_split, 
            random_state=42, stratify=y
        )
        
        print(f"训练集大小: {X_train.shape}, 验证集大小: {X_val.shape}")
        print(f"训练集标签分布: {pd.Series(y_train).value_counts().to_dict()}")
        
        # 训练XGBoost - 兼容不同版本
        print("训练XGBoost模型...")
        try:
            # 尝试新版本的方式
            self.xgb_model.fit(
                X_train, y_train,
                eval_set=[(X_val, y_val)],
                verbose=False
            )
        except TypeError:
            # 如果失败，使用旧版本的方式
            try:
                self.xgb_model.fit(
                    X_train, y_train,
                    eval_set=[(X_val, y_val)],
                    early_stopping_rounds=50,
                    verbose=False
                )
            except TypeError:
                # 如果还是失败，只用训练集训练
                print("⚠ 使用简化的XGBoost训练方式")
                self.xgb_model.fit(X_train, y_train)
        
        # 训练随机森林
        print("训练随机森林模型...")
        self.rf_model.fit(X_train, y_train)
        
        # 获取验证集预测概率 - 类似PyTorch的验证阶段
        xgb_probs = self.xgb_model.predict_proba(X_val)[:, 1]
        rf_probs = self.rf_model.predict_proba(X_val)[:, 1]
        
        # 计算最优权重 - 类似PyTorch的超参数优化
        print("计算最优集成权重...")
        best_auc = 0
        best_weights = [0.5, 0.5]
        
        # 网格搜索最优权重
        for w1 in np.arange(0.1, 1.0, 0.1):
            w2 = 1 - w1
            ensemble_probs = w1 * xgb_probs + w2 * rf_probs
            auc = roc_auc_score(y_val, ensemble_probs)
            
            if auc > best_auc:
                best_auc = auc
                best_weights = [w1, w2]
        
        self.ensemble_weights = best_weights
        print(f"最优权重 - XGBoost: {best_weights[0]:.3f}, RF: {best_weights[1]:.3f}")
        print(f"验证集AUC: {best_auc:.4f}")
        
        # 评估各模型性能
        xgb_auc = roc_auc_score(y_val, xgb_probs)
        rf_auc = roc_auc_score(y_val, rf_probs)
        
        print(f"XGBoost单独AUC: {xgb_auc:.4f}")
        print(f"随机森林单独AUC: {rf_auc:.4f}")
        print(f"软集成AUC: {best_auc:.4f}")
        
        self.is_fitted = True
        
        return {
            'xgb_auc': xgb_auc,
            'rf_auc': rf_auc,
            'ensemble_auc': best_auc,
            'weights': best_weights
        }
    
    def predict_proba(self, X):
        """预测概率 - 类似PyTorch的推理"""
        if not self.is_fitted:
            raise ValueError("模型尚未训练，请先调用fit方法")
        
        X_processed = self._preprocess_data(X, fit=False)
        
        # 获取各模型预测概率
        xgb_probs = self.xgb_model.predict_proba(X_processed)[:, 1]
        rf_probs = self.rf_model.predict_proba(X_processed)[:, 1]
        
        # 软集成 - 类似PyTorch的ensemble layer
        ensemble_probs = (self.ensemble_weights[0] * xgb_probs + 
                         self.ensemble_weights[1] * rf_probs)
        
        return ensemble_probs
    
    def predict(self, X, threshold=0.5):
        """预测类别"""
        probs = self.predict_proba(X)
        return (probs >= threshold).astype(int)

def save_soft_ensemble_model(model, model_name, performance_metrics, feature_names):
    """保存软集成模型为.model格式 - 参考train_ensemble_model.py风格"""
    
    # 确保模型目录存在
    Path("model").mkdir(exist_ok=True)
    
    model_data = {
        # 模型组件
        'xgb_model': model.xgb_model,
        'rf_model': model.rf_model,
        'ensemble_weights': model.ensemble_weights,
        
        # 预处理器
        'imputer': model.imputer,
        'scaler': model.scaler,
        
        # 元数据
        'feature_names': feature_names,
        'use_gpu': model.use_gpu,
        'performance_metrics': performance_metrics,
        'model_type': 'SoftEnsembleXGBRF',
        
        # 模型参数
        'xgb_params': model.xgb_params,
        'rf_params': model.rf_params,
        
        # 版本信息
        'xgboost_version': xgb.__version__,
        'sklearn_version': __import__('sklearn').__version__
    }
    
    model_path = f"model/{model_name}.model"
    joblib.dump(model_data, model_path)
    
    # 单独保存预处理器 - 兼容其他代码
    imputer_path = './model/ensemble_imputer.pkl'
    minmax_path = './model/ensemble_minmax.pkl'
    
    joblib.dump(model.imputer, imputer_path)
    joblib.dump(model.scaler, minmax_path)
    
    absolute_path = Path(model_path).absolute()
    file_size = Path(model_path).stat().st_size / 1024
    
    print(f"✓ 软集成模型已保存:")
    print(f"  主模型文件: {model_path}")
    print(f"  预处理器文件: {imputer_path}")
    print(f"  标准化器文件: {minmax_path}")
    print(f"  绝对路径: {absolute_path}")
    print(f"  文件大小: {file_size:.2f} KB")
    
    return model_path

def load_soft_ensemble_model(model_path):
    """加载软集成模型"""
    model_data = joblib.load(model_path)
    
    # 重建模型
    model = SoftEnsembleXGBRF(use_gpu=model_data['use_gpu'])
    model.xgb_model = model_data['xgb_model']
    model.rf_model = model_data['rf_model']
    model.ensemble_weights = model_data['ensemble_weights']
    model.imputer = model_data['imputer']
    model.scaler = model_data['scaler']
    model.feature_names = model_data['feature_names']
    model.is_fitted = True
    
    return model, model_data['performance_metrics']

def train_soft_ensemble_with_feat73(use_gpu=True):
    """使用73维最优特征训练软集成模型 - 参考train_ensemble_model.py"""
    
    print("=" * 60)
    print("软集成XGBoost+随机森林训练 (73维最优特征)")
    print("=" * 60)
    
    # 1. 读取73维最优特征名称
    print("读取73维最优特征...")
    feat_file = './results/AAAA_featureSelect/feat_importance_sorted_rename.csv'
    
    if not os.path.exists(feat_file):
        print(f"特征文件不存在: {feat_file}")
        # 使用默认特征名称
        feat73_cols = [f"feat_{i+1}" for i in range(73)]
        print("使用默认的73个特征名称")
    else:
        feat146_cols = pd.read_csv(feat_file)
        feat73_cols = feat146_cols['feat_name'][:73].tolist()
        print(f"成功读取73个最优特征")
    
    # 2. 读取训练数据
    print("读取训练数据...")
    train_file = './data/feature-encoded/for-train-test/train-closeby6-encoded-feat146.csv'
    
    if not os.path.exists(train_file):
        # 尝试其他可能的文件路径
        alternative_files = [
            './data/feature-encoded/train_pos_neg_closeby6_20250118-manual-del.csv',
            './data/feature-encoded/train_pos_neg_closeby6.csv'
        ]
        
        train_file = None
        for alt_file in alternative_files:
            if os.path.exists(alt_file):
                train_file = alt_file
                break
        
        if train_file is None:
            raise FileNotFoundError("未找到训练数据文件")
    
    train_data = pd.read_csv(train_file)
    print(f"训练数据形状: {train_data.shape}")
    
    # 3. 检查特征是否存在，如果不存在则重命名
    missing_features = [feat for feat in feat73_cols if feat not in train_data.columns]
    if missing_features:
        print(f"缺失特征数量: {len(missing_features)}")
        
        # 尝试使用数值列作为特征
        numeric_cols = train_data.select_dtypes(include=[np.number]).columns.tolist()
        
        # 排除可能的标签列
        label_candidates = ['label', '#class', 'class', 'target', 'y']
        for label_candidate in label_candidates:
            if label_candidate in numeric_cols:
                numeric_cols.remove(label_candidate)
        
        feat73_cols = numeric_cols[:73] if len(numeric_cols) >= 73 else numeric_cols
        print(f"使用可用的数值特征: {len(feat73_cols)}个")
    
    # 4. 确定标签列
    label_candidates = ['label', '#class', 'class', 'target', 'y']
    label_col = None
    for candidate in label_candidates:
        if candidate in train_data.columns:
            label_col = candidate
            break
    
    if label_col is None:
        label_col = train_data.columns[-1]
        print(f"使用最后一列作为标签: {label_col}")
    else:
        print(f"使用标签列: {label_col}")
    
    # 5. 准备特征和标签
    X = train_data[feat73_cols]
    y = train_data[label_col]
    
    print(f"特征数据形状: {X.shape}")
    print(f"标签分布: {y.value_counts().to_dict()}")
    
    # 6. 训练软集成模型
    model = SoftEnsembleXGBRF(use_gpu=use_gpu)
    performance = model.fit(X, y, validation_split=0.2)
    
    # 7. 交叉验证评估
    print("\n进行5折交叉验证...")
    X_processed = model._preprocess_data(X, fit=False)
    
    xgb_cv_scores = cross_val_score(model.xgb_model, X_processed, y, cv=5, scoring='roc_auc')
    rf_cv_scores = cross_val_score(model.rf_model, X_processed, y, cv=5, scoring='roc_auc')
    
    print(f"XGBoost CV AUC: {xgb_cv_scores.mean():.4f} ± {xgb_cv_scores.std():.4f}")
    print(f"随机森林 CV AUC: {rf_cv_scores.mean():.4f} ± {rf_cv_scores.std():.4f}")
    
    # 8. 保存性能指标
    final_performance = {
        **performance,
        'xgb_cv_auc_mean': xgb_cv_scores.mean(),
        'xgb_cv_auc_std': xgb_cv_scores.std(),
        'rf_cv_auc_mean': rf_cv_scores.mean(),
        'rf_cv_auc_std': rf_cv_scores.std(),
        'feature_count': len(feat73_cols),
        'training_samples': len(X)
    }
    
    # 9. 保存模型 - 参考train_ensemble_model.py的命名方式
    model_name = f"soft_ensemble_xgb_rf_feat73{'_gpu' if use_gpu else ''}"
    model_path = save_soft_ensemble_model(model, model_name, final_performance, feat73_cols)
    
    # 10. 保存特征名称 - 参考train_ensemble_model.py
    with open('./model/soft_ensemble_feat73_names.json', 'w') as f:
        json.dump(feat73_cols, f, indent=2)
    
    # 11. 保存训练报告
    report = {
        'model_name': model_name,
        'model_path': model_path,
        'feature_count': len(feat73_cols),
        'training_samples': len(X),
        'performance_metrics': final_performance,
        'ensemble_weights': {
            'xgboost': model.ensemble_weights[0],
            'random_forest': model.ensemble_weights[1]
        },
        'use_gpu': use_gpu,
        'features_used': feat73_cols
    }
    
    with open('./model/soft_ensemble_training_report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n✅ 训练完成！")
    print(f"模型文件: {model_path}")
    print(f"特征文件: ./model/soft_ensemble_feat73_names.json")
    print(f"训练报告: ./model/soft_ensemble_training_report.json")
    print(f"最终集成AUC: {performance['ensemble_auc']:.4f}")
    print(f"实际使用特征数: {len(feat73_cols)}")
    
    return model, model_path, final_performance

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='训练软集成XGBoost+随机森林模型')
    parser.add_argument('--gpu', action='store_true', help='使用GPU加速XGBoost')
    parser.add_argument('--no-gpu', dest='gpu', action='store_false', help='使用CPU训练')
    parser.set_defaults(gpu=True)
    
    args = parser.parse_args()
    
    try:
        model, model_path, performance = train_soft_ensemble_with_feat73(use_gpu=args.gpu)
        print(f"\n🎉 训练成功完成！")
        print(f"模型已保存至: {model_path}")
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

