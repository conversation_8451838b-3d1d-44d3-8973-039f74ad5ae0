import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import pandas as pd
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import LabelEncoder
import warnings
warnings.filterwarnings("ignore")

class DNASequenceDataset(Dataset):
    """DNA序列数据集类"""
    
    def __init__(self, sequences, labels=None):
        """
        初始化DNA序列数据集
        
        Args:
            sequences: DNA序列列表
            labels: 标签列表（可选，用于训练）
        """
        self.sequences = sequences
        self.labels = labels
        self.seq_length = 128  # 固定序列长度
        
        # DNA碱基到数字的映射
        self.base_to_idx = {'A': 0, 'T': 1, 'G': 2, 'C': 3, 'N': 4}
        
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        sequence = self.sequences[idx]
        
        # 将DNA序列编码为数字
        encoded_seq = self.encode_sequence(sequence)
        
        if self.labels is not None:
            return torch.FloatTensor(encoded_seq), torch.LongTensor([self.labels[idx]])
        else:
            return torch.FloatTensor(encoded_seq)
    
    def encode_sequence(self, sequence):
        """
        将DNA序列编码为one-hot向量
        
        Args:
            sequence: DNA序列字符串
            
        Returns:
            encoded: one-hot编码的序列 (4, seq_length)
        """
        # 确保序列长度为128bp
        if len(sequence) > self.seq_length:
            sequence = sequence[:self.seq_length]
        elif len(sequence) < self.seq_length:
            sequence = sequence + 'N' * (self.seq_length - len(sequence))
        
        # 创建one-hot编码矩阵
        encoded = np.zeros((4, self.seq_length))
        
        for i, base in enumerate(sequence.upper()):
            if base in self.base_to_idx and base != 'N':
                base_idx = self.base_to_idx[base]
                encoded[base_idx, i] = 1
        
        return encoded

class DNACNNModel(nn.Module):
    """DNA序列CNN模型"""
    
    def __init__(self, seq_length=128, num_filters=64, filter_sizes=[3, 5, 7], 
                 dropout_rate=0.5, feature_dim=128):
        """
        初始化CNN模型
        
        Args:
            seq_length: DNA序列长度
            num_filters: 每个卷积核的数量
            filter_sizes: 卷积核大小列表
            dropout_rate: dropout比率
            feature_dim: 输出特征维度
        """
        super(DNACNNModel, self).__init__()
        
        self.seq_length = seq_length
        self.num_filters = num_filters
        self.filter_sizes = filter_sizes
        self.feature_dim = feature_dim
        
        # 多尺度卷积层
        self.convs = nn.ModuleList([
            nn.Conv1d(in_channels=4, out_channels=num_filters, 
                     kernel_size=filter_size, padding=filter_size//2)
            for filter_size in filter_sizes
        ])
        
        # 批归一化层
        self.batch_norms = nn.ModuleList([
            nn.BatchNorm1d(num_filters) for _ in filter_sizes
        ])
        
        # 全局最大池化
        self.global_max_pool = nn.AdaptiveMaxPool1d(1)
        
        # 全连接层
        total_filters = num_filters * len(filter_sizes)
        self.fc1 = nn.Linear(total_filters, 256)
        self.fc2 = nn.Linear(256, feature_dim)
        
        # Dropout层
        self.dropout = nn.Dropout(dropout_rate)
        
        # 激活函数
        self.relu = nn.ReLU()
        
    def forward(self, x):
        """
        前向传播
        
        Args:
            x: 输入序列 (batch_size, 4, seq_length)
            
        Returns:
            features: 提取的特征 (batch_size, feature_dim)
        """
        conv_outputs = []
        
        # 多尺度卷积特征提取
        for conv, bn in zip(self.convs, self.batch_norms):
            conv_out = conv(x)  # (batch_size, num_filters, seq_length)
            conv_out = bn(conv_out)
            conv_out = self.relu(conv_out)
            
            # 全局最大池化
            pooled = self.global_max_pool(conv_out)  # (batch_size, num_filters, 1)
            pooled = pooled.squeeze(-1)  # (batch_size, num_filters)
            
            conv_outputs.append(pooled)
        
        # 拼接所有卷积特征
        features = torch.cat(conv_outputs, dim=1)  # (batch_size, total_filters)
        
        # 全连接层
        features = self.dropout(features)
        features = self.fc1(features)
        features = self.relu(features)
        features = self.dropout(features)
        features = self.fc2(features)
        
        return features

class DNAFeatureExtractor:
    """DNA序列特征提取器"""
    
    def __init__(self, model_path=None, device=None):
        """
        初始化特征提取器
        
        Args:
            model_path: 预训练模型路径
            device: 计算设备
        """
        self.device = device if device else torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = DNACNNModel()
        self.model.to(self.device)
        
        if model_path:
            self.load_model(model_path)
    
    def load_model(self, model_path):
        """加载预训练模型"""
        self.model.load_state_dict(torch.load(model_path, map_location=self.device))
        self.model.eval()
    
    def save_model(self, model_path):
        """保存模型"""
        torch.save(self.model.state_dict(), model_path)
    
    def extract_features(self, sequences, batch_size=32):
        """
        提取DNA序列特征
        
        Args:
            sequences: DNA序列列表
            batch_size: 批处理大小
            
        Returns:
            features: 提取的特征矩阵 (n_samples, feature_dim)
        """
        dataset = DNASequenceDataset(sequences)
        dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=False)
        
        features_list = []
        
        self.model.eval()
        with torch.no_grad():
            for batch in dataloader:
                batch = batch.to(self.device)
                features = self.model(batch)
                features_list.append(features.cpu().numpy())
        
        return np.vstack(features_list)
    
    def train_model(self, train_sequences, train_labels, val_sequences=None, val_labels=None,
                   epochs=50, batch_size=32, learning_rate=0.001):
        """
        训练CNN模型
        
        Args:
            train_sequences: 训练序列
            train_labels: 训练标签
            val_sequences: 验证序列
            val_labels: 验证标签
            epochs: 训练轮数
            batch_size: 批处理大小
            learning_rate: 学习率
        """
        # 创建数据集
        train_dataset = DNASequenceDataset(train_sequences, train_labels)
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        
        if val_sequences is not None:
            val_dataset = DNASequenceDataset(val_sequences, val_labels)
            val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
        
        # 定义损失函数和优化器
        criterion = nn.CrossEntropyLoss()
        optimizer = torch.optim.Adam(self.model.parameters(), lr=learning_rate)
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5)
        
        # 训练循环
        for epoch in range(epochs):
            # 训练阶段
            self.model.train()
            train_loss = 0.0
            train_correct = 0
            train_total = 0
            
            for sequences, labels in train_loader:
                sequences = sequences.to(self.device)
                labels = labels.to(self.device).squeeze()
                
                optimizer.zero_grad()
                
                # 前向传播
                features = self.model(sequences)
                
                # 添加分类头进行训练
                if not hasattr(self.model, 'classifier'):
                    self.model.classifier = nn.Linear(self.model.feature_dim, 2).to(self.device)
                
                outputs = self.model.classifier(features)
                loss = criterion(outputs, labels)
                
                # 反向传播
                loss.backward()
                optimizer.step()
                
                train_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                train_total += labels.size(0)
                train_correct += (predicted == labels).sum().item()
            
            train_acc = 100 * train_correct / train_total
            
            # 验证阶段
            if val_sequences is not None:
                val_loss, val_acc = self._validate(val_loader, criterion)
                scheduler.step(val_loss)
                
                print(f'Epoch [{epoch+1}/{epochs}], '
                      f'Train Loss: {train_loss/len(train_loader):.4f}, Train Acc: {train_acc:.2f}%, '
                      f'Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%')
            else:
                print(f'Epoch [{epoch+1}/{epochs}], '
                      f'Train Loss: {train_loss/len(train_loader):.4f}, Train Acc: {train_acc:.2f}%')
    
    def _validate(self, val_loader, criterion):
        """验证模型"""
        self.model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0
        
        with torch.no_grad():
            for sequences, labels in val_loader:
                sequences = sequences.to(self.device)
                labels = labels.to(self.device).squeeze()
                
                features = self.model(sequences)
                outputs = self.model.classifier(features)
                loss = criterion(outputs, labels)
                
                val_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                val_total += labels.size(0)
                val_correct += (predicted == labels).sum().item()
        
        val_acc = 100 * val_correct / val_total
        return val_loss / len(val_loader), val_acc

def load_dna_sequences(pos_file, neg_file):
    """
    加载DNA序列数据
    
    Args:
        pos_file: 正样本序列文件路径
        neg_file: 负样本序列文件路径
        
    Returns:
        sequences: 序列列表
        labels: 标签列表
        metadata: 元数据列表
    """
    sequences = []
    labels = []
    metadata = []
    
    # 读取正样本
    with open(pos_file, 'r') as f:
        for line in f:
            parts = line.strip().split('\t')
            if len(parts) >= 5:
                chrom, pos, ref, alt, seq = parts[:5]
                sequences.append(seq)
                labels.append(1)
                metadata.append({'chrom': chrom, 'pos': pos, 'ref': ref, 'alt': alt})
    
    # 读取负样本
    with open(neg_file, 'r') as f:
        for line in f:
            parts = line.strip().split('\t')
            if len(parts) >= 5:
                chrom, pos, ref, alt, seq = parts[:5]
                sequences.append(seq)
                labels.append(0)
                metadata.append({'chrom': chrom, 'pos': pos, 'ref': ref, 'alt': alt})
    
    return sequences, labels, metadata
