#!/usr/bin/env python3
"""
结果比较脚本 - 比较组合模型与原始模型的性能
"""

import pandas as pd
import numpy as np
import json
import os
import matplotlib.pyplot as plt

def load_results():
    """加载各种结果文件"""
    results = {}
    
    # 1. 加载我们的组合模型结果
    if os.path.exists('./results/simple_test_summary.csv'):
        combined_results = pd.read_csv('./results/simple_test_summary.csv')
        results['combined_model'] = combined_results
        print("✓ 加载组合模型结果")
    else:
        print("✗ 未找到组合模型结果")
    
    # 2. 加载原始模型结果
    original_files = [
        './results/test1_xgb_rf_ada_results.csv',
        './results/test2_xgb_rf_ada_results.csv'
    ]
    
    original_results = []
    for i, file_path in enumerate(original_files):
        if os.path.exists(file_path):
            df = pd.read_csv(file_path)
            df['test_set'] = f'Test Set {i+1}'
            original_results.append(df)
            print(f"✓ 加载原始模型结果: {file_path}")
        else:
            print(f"✗ 未找到原始模型结果: {file_path}")
    
    if original_results:
        results['original_model'] = pd.concat(original_results, ignore_index=True)
    
    # 3. 加载训练报告
    if os.path.exists('./model/simple_training_report.json'):
        with open('./model/simple_training_report.json', 'r') as f:
            results['training_report'] = json.load(f)
        print("✓ 加载训练报告")
    
    return results

def create_comparison_table(results):
    """创建比较表格"""
    print("\n=== 性能比较 ===")
    
    if 'combined_model' not in results:
        print("缺少组合模型结果")
        return
    
    combined_df = results['combined_model']
    
    # 显示组合模型结果
    print("\n🧬 组合模型 (传统特征 + 序列特征):")
    print("=" * 60)
    print(f"{'测试集':<12} {'样本数':<8} {'AUC':<8} {'AUPR':<8} {'ACC':<8} {'F1':<8}")
    print("-" * 60)
    
    for _, row in combined_df.iterrows():
        print(f"{row['test_set']:<12} {row['n_samples']:<8} {row['AUC']:<8.4f} {row['AUPR']:<8.4f} {row['ACC']:<8.4f} {row['F1']:<8.4f}")
    
    # 如果有原始模型结果，进行比较
    if 'original_model' in results:
        original_df = results['original_model']
        print(f"\n📊 原始模型 (仅传统特征):")
        print("=" * 60)
        print(f"{'测试集':<12} {'样本数':<8} {'AUC':<8} {'AUPR':<8} {'ACC':<8} {'F1':<8}")
        print("-" * 60)
        
        for _, row in original_df.iterrows():
            n_samples = row.get('n_samples', 'N/A')
            print(f"{row['test_set']:<12} {n_samples:<8} {row['AUC']:<8.4f} {row['AUPR']:<8.4f} {row['ACC']:<8.4f} {row['F1']:<8.4f}")
        
        # 计算改进
        print(f"\n📈 性能改进 (组合模型 vs 原始模型):")
        print("=" * 60)
        print(f"{'测试集':<12} {'AUC改进':<10} {'AUPR改进':<10} {'ACC改进':<10} {'F1改进':<10}")
        print("-" * 60)
        
        for _, combined_row in combined_df.iterrows():
            test_set = combined_row['test_set']
            original_row = original_df[original_df['test_set'] == test_set]
            
            if not original_row.empty:
                original_row = original_row.iloc[0]
                
                auc_improvement = combined_row['AUC'] - original_row['AUC']
                aupr_improvement = combined_row['AUPR'] - original_row['AUPR']
                acc_improvement = combined_row['ACC'] - original_row['ACC']
                f1_improvement = combined_row['F1'] - original_row['F1']
                
                print(f"{test_set:<12} {auc_improvement:+8.4f} {aupr_improvement:+8.4f} {acc_improvement:+8.4f} {f1_improvement:+8.4f}")

def create_feature_analysis(results):
    """创建特征分析"""
    print(f"\n=== 特征分析 ===")
    
    if 'training_report' in results:
        report = results['training_report']
        features = report['features']
        
        print(f"特征维度对比:")
        print(f"  传统特征: {features['traditional']} 维")
        print(f"  序列特征: {features['sequence']} 维")
        print(f"  总特征:   {features['total']} 维")
        print(f"  维度提升: {features['sequence']} 维 ({features['sequence']/features['traditional']*100:.1f}%)")
        
        print(f"\n训练样本分布:")
        samples = report['samples']
        print(f"  总样本数: {samples['total']}")
        print(f"  训练集:   {samples['train']} ({samples['train']/samples['total']*100:.1f}%)")
        print(f"  测试集:   {samples['test']} ({samples['test']/samples['total']*100:.1f}%)")
        
        print(f"\n训练集性能:")
        train_perf = report['performance']['train']
        print(f"  AUC:  {train_perf['AUC']:.4f}")
        print(f"  AUPR: {train_perf['AUPR']:.4f}")
        print(f"  ACC:  {train_perf['ACC']:.4f}")
        print(f"  F1:   {train_perf['F1']:.4f}")

def create_visualization(results):
    """创建可视化图表"""
    print(f"\n=== 生成可视化图表 ===")
    
    if 'combined_model' not in results:
        print("缺少组合模型结果，跳过可视化")
        return
    
    try:
        combined_df = results['combined_model']
        
        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # 子图1: AUC比较
        ax1 = axes[0, 0]
        test_sets = combined_df['test_set']
        auc_values = combined_df['AUC']
        
        bars1 = ax1.bar(test_sets, auc_values, color='skyblue', alpha=0.7)
        ax1.set_ylabel('AUC')
        ax1.set_title('AUC Performance')
        ax1.set_ylim(0, 1)
        
        # 在柱子上添加数值
        for bar, value in zip(bars1, auc_values):
            ax1.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.01,
                    f'{value:.3f}', ha='center', va='bottom')
        
        # 子图2: AUPR比较
        ax2 = axes[0, 1]
        aupr_values = combined_df['AUPR']
        
        bars2 = ax2.bar(test_sets, aupr_values, color='lightcoral', alpha=0.7)
        ax2.set_ylabel('AUPR')
        ax2.set_title('AUPR Performance')
        ax2.set_ylim(0, 1)
        
        for bar, value in zip(bars2, aupr_values):
            ax2.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.01,
                    f'{value:.3f}', ha='center', va='bottom')
        
        # 子图3: 多指标雷达图
        ax3 = axes[1, 0]
        metrics = ['AUC', 'AUPR', 'ACC', 'F1']
        
        for i, (_, row) in enumerate(combined_df.iterrows()):
            values = [row[metric] for metric in metrics]
            ax3.plot(metrics, values, 'o-', label=row['test_set'], alpha=0.7)
        
        ax3.set_ylabel('Performance')
        ax3.set_title('Multi-metric Performance')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        ax3.set_ylim(0, 1)
        
        # 子图4: 特征维度饼图
        ax4 = axes[1, 1]
        if 'training_report' in results:
            features = results['training_report']['features']
            sizes = [features['traditional'], features['sequence']]
            labels = ['Traditional Features', 'Sequence Features']
            colors = ['lightblue', 'lightgreen']
            
            ax4.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
            ax4.set_title('Feature Composition')
        else:
            ax4.text(0.5, 0.5, 'No feature data', ha='center', va='center', transform=ax4.transAxes)
            ax4.set_title('Feature Composition')
        
        plt.tight_layout()
        
        # 保存图表
        os.makedirs('./results/plots', exist_ok=True)
        plt.savefig('./results/plots/performance_comparison.png', dpi=300, bbox_inches='tight')
        print("✓ 图表已保存到 ./results/plots/performance_comparison.png")
        
        plt.show()
        
    except Exception as e:
        print(f"✗ 可视化生成失败: {e}")

def save_comparison_report(results):
    """保存比较报告"""
    print(f"\n=== 保存比较报告 ===")
    
    try:
        os.makedirs('./results', exist_ok=True)
        
        # 创建详细报告
        report = {
            'comparison_type': 'Combined Model vs Original Model',
            'timestamp': pd.Timestamp.now().isoformat(),
            'model_info': {
                'combined_model': {
                    'description': 'Traditional features + Sequence features',
                    'feature_count': results.get('training_report', {}).get('features', {}).get('total', 'N/A'),
                    'traditional_features': results.get('training_report', {}).get('features', {}).get('traditional', 'N/A'),
                    'sequence_features': results.get('training_report', {}).get('features', {}).get('sequence', 'N/A')
                }
            },
            'results': {}
        }
        
        if 'combined_model' in results:
            report['results']['combined_model'] = results['combined_model'].to_dict('records')
        
        if 'original_model' in results:
            report['results']['original_model'] = results['original_model'].to_dict('records')
        
        if 'training_report' in results:
            report['training_info'] = results['training_report']
        
        # 保存JSON报告
        with open('./results/comparison_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        print("✓ 比较报告已保存到 ./results/comparison_report.json")
        
    except Exception as e:
        print(f"✗ 保存报告失败: {e}")

def main():
    """主函数"""
    print("📊 模型性能比较分析")
    print("=" * 50)
    
    # 加载结果
    results = load_results()
    
    if not results:
        print("未找到任何结果文件")
        return 1
    
    # 创建比较表格
    create_comparison_table(results)
    
    # 特征分析
    create_feature_analysis(results)
    
    # 生成可视化
    create_visualization(results)
    
    # 保存报告
    save_comparison_report(results)
    
    print(f"\n🎉 比较分析完成!")
    print("查看 ./results/ 目录获取详细结果")
    
    return 0

if __name__ == "__main__":
    exit(main())
