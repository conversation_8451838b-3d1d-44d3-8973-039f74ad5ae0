["MACIE01", "MACIE10", "MACIE00", "MACIE11", "MACIE_conserved", "MACIE_regulatory", "MACIE_anyclass", "FunSeq_Score", "GenoCanyon_Score", "FIRE_Score", "ReMM_Score", "RegSeq0", "RegSeq1", "RegSeq2", "RegSeq3", "RegSeq4", "RegSeq5", "RegSeq6", "RegSeq7", "SpliceAI-acc-gain", "SpliceAI-acc-loss", "SpliceAI-don-gain", "SpliceAI-don-loss", "MMSp_acceptor", "MMSp_exon", "MMSp_donor", "siPhy_rankscore", "integrated_fitCons_score", "integrated_confidence_value", "GM12878_fitCons_score", "GM12878_confidence_value", "H1-hESC_fitCons_score", "H1-hESC_confidence_value", "HUVEC_fitCons_score", "HUVEC_confidence_value", "integrated_fitCons_score_rankscore", "GM12878_fitCons_score_rankscore", "H1-hESC_fitCons_score_rankscore", "HUVEC_fitCons_score_rankscore", "priPhCons", "mamPhCons", "verPhCons", "priPhyloP", "mamPhyloP", "verPhyloP", "bStatistic", "GerpRS", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "GerpN", "GerpS", "ZooPriPhyloP", "ZooVerPhyloP", "EncodeH3K4me1-sum", "EncodeH3K4me1-max", "EncodeH3K4me2-sum", "EncodeH3K4me2-max", "EncodeH3K4me3-sum", "EncodeH3K4me3-max", "EncodeH3K9ac-sum", "EncodeH3K9ac-max", "EncodeH3K9me3-sum", "EncodeH3K9me3-max", "EncodeH3K27ac-sum", "EncodeH3K27ac-max", "EncodeH3K27me3-sum", "EncodeH3K27me3-max", "EncodeH3K36me3-sum", "EncodeH3K36me3-max", "EncodeH3K79me2-sum", "EncodeH3K79me2-max", "EncodeH4K20me1-sum", "EncodeH4K20me1-max", "EncodeH2AFZ-sum", "EncodeH2AFZ-max", "EncodeDNase-sum", "EncodeDNase-max", "EncodetotalRNA-sum", "EncodetotalRNA-max", "cHmm_E1", "cHmm_E2", "cHmm_E3", "cHmm_E4", "cHmm_E5", "cHmm_E6", "cHmm_E7", "cHmm_E8", "cHmm_E9", "cHmm_E10", "cHmm_E11", "cHmm_E12", "cHmm_E13", "cHmm_E14", "cHmm_E15", "cHmm_E16", "cHmm_E17", "cHmm_E18", "cHmm_E19", "cHmm_E20", "cHmm_E21", "cHmm_E22", "cHmm_E23", "cHmm_E24", "cHmm_E25", "RemapOverlapTF", "RemapOverlapCL", "k24_bismap", "k24_umap", "k36_bismap", "k36_umap", "k50_bismap", "k50_umap", "k100_bismap", "k100_umap", "nucdiv", "recombination_rate", "Dist2Mutation", "Freq100bp", "Rare100bp", "Sngl100bp", "Freq1000bp", "Rare1000bp", "Sngl1000bp", "Freq10000bp", "Rare10000bp", "Sngl10000bp", "TFBs", "TE", "dPSIZ", "DSP", "RSCU", "dRSCU", "CpG?", "CpG_exon", "SR-", "SR+", "FAS6-", "FAS6+", "MEC-MC?", "MEC-CS?", "MES-KM?", "PESE-", "PESE+", "PESS-", "PESS+", "f_premrna", "f_mrna"]