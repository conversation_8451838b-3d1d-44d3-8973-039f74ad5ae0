#!/usr/bin/env python3
"""
完整评估脚本 - 使用正确的DNA序列文件夹结构
分别评估训练集、测试集1和测试集2的性能
"""

import sys
import os
import pandas as pd
import numpy as np
import json
import joblib
from sklearn.model_selection import train_test_split

# 添加code目录到路径
sys.path.append('./code')
sys.path.append('.')

try:
    from utils import rename_with_feat40, metricsScores
    print("✓ 成功导入utils模块")
except ImportError as e:
    print(f"✗ 导入utils失败: {e}")
    sys.exit(1)

def load_dna_sequences_from_folder(folder_path):
    """从指定文件夹加载DNA序列"""
    sequences = []
    labels = []
    
    pos_file = os.path.join(folder_path, f"{os.path.basename(folder_path)}_pos_seq.txt")
    neg_file = os.path.join(folder_path, f"{os.path.basename(folder_path)}_neg_seq.txt")
    
    # 处理特殊的文件名格式
    if not os.path.exists(pos_file):
        # 尝试其他可能的文件名
        possible_pos_files = [
            os.path.join(folder_path, "train_pos_seq.txt"),
            os.path.join(folder_path, "test1_pos_seq.txt"), 
            os.path.join(folder_path, "test2_pos_seq.txt"),
            os.path.join(folder_path, "pos_seq.txt")
        ]
        for pf in possible_pos_files:
            if os.path.exists(pf):
                pos_file = pf
                break
    
    if not os.path.exists(neg_file):
        # 尝试其他可能的文件名
        possible_neg_files = [
            os.path.join(folder_path, "train_neg_seq.txt"),
            os.path.join(folder_path, "test1_neg_seq.txt"),
            os.path.join(folder_path, "test2_neg_seq.txt"),
            os.path.join(folder_path, "neg_seq.txt")
        ]
        for nf in possible_neg_files:
            if os.path.exists(nf):
                neg_file = nf
                break
    
    print(f"  正样本文件: {pos_file}")
    print(f"  负样本文件: {neg_file}")
    
    # 加载正样本
    if os.path.exists(pos_file):
        try:
            with open(pos_file, 'r') as f:
                for line in f:
                    parts = line.strip().split('\t')
                    if len(parts) >= 5:
                        seq = parts[4]
                        sequences.append(seq)
                        labels.append(1)
            print(f"  ✓ 加载了 {sum(labels)} 个正样本")
        except Exception as e:
            print(f"  ✗ 加载正样本失败: {e}")
            return [], []
    else:
        print(f"  ✗ 正样本文件不存在: {pos_file}")
        return [], []
    
    # 加载负样本
    if os.path.exists(neg_file):
        try:
            with open(neg_file, 'r') as f:
                for line in f:
                    parts = line.strip().split('\t')
                    if len(parts) >= 5:
                        seq = parts[4]
                        sequences.append(seq)
                        labels.append(0)
            print(f"  ✓ 加载了 {len(labels) - sum(labels)} 个负样本")
        except Exception as e:
            print(f"  ✗ 加载负样本失败: {e}")
            return [], []
    else:
        print(f"  ✗ 负样本文件不存在: {neg_file}")
        return [], []
    
    return sequences, labels

def encode_dna_sequence_simple(sequence, seq_length=128):
    """简单的DNA序列编码函数"""
    # 确保序列长度为128bp
    if len(sequence) > seq_length:
        sequence = sequence[:seq_length]
    elif len(sequence) < seq_length:
        sequence = sequence + 'N' * (seq_length - len(sequence))
    
    # 简单的数值编码: A=0, T=1, G=2, C=3, N=4
    base_to_num = {'A': 0, 'T': 1, 'G': 2, 'C': 3, 'N': 4}
    encoded = []
    
    for base in sequence.upper():
        encoded.append(base_to_num.get(base, 4))
    
    return encoded

def extract_sequence_features_simple(sequences):
    """简单的序列特征提取"""
    print("  提取序列特征...")
    
    features = []
    for seq in sequences:
        # 编码序列
        encoded = encode_dna_sequence_simple(seq)
        
        # 提取简单的统计特征
        seq_features = []
        
        # 碱基组成
        for base_num in range(5):  # A, T, G, C, N
            count = encoded.count(base_num)
            seq_features.append(count / len(encoded))
        
        # GC含量
        gc_count = encoded.count(2) + encoded.count(3)  # G + C
        seq_features.append(gc_count / len(encoded))
        
        # 连续碱基统计
        for base_num in range(4):
            max_consecutive = 0
            current_consecutive = 0
            for e in encoded:
                if e == base_num:
                    current_consecutive += 1
                    max_consecutive = max(max_consecutive, current_consecutive)
                else:
                    current_consecutive = 0
            seq_features.append(max_consecutive / len(encoded))
        
        # 二核苷酸频率 (简化版)
        dinucleotides = ['AA', 'AT', 'AG', 'AC', 'TT', 'TG', 'TC', 'GG', 'GC', 'CC']
        seq_str = ''.join([['A', 'T', 'G', 'C', 'N'][e] for e in encoded])
        for dinuc in dinucleotides:
            count = seq_str.count(dinuc)
            seq_features.append(count / max(1, len(seq_str) - 1))
        
        features.append(seq_features)
    
    return np.array(features)

def evaluate_dataset(dataset_name, feature_file, seq_folder, model, imputer, scaler, feature_info):
    """评估指定数据集"""
    print(f"\n=== 评估 {dataset_name} ===")
    
    # 1. 加载传统特征数据
    print("1. 加载传统特征数据...")
    try:
        if not os.path.exists(feature_file):
            print(f"  ✗ 特征文件不存在: {feature_file}")
            return None
            
        feature_data, _ = rename_with_feat40(feature_file)
        print(f"  ✓ 传统特征数据形状: {feature_data.shape}")
    except Exception as e:
        print(f"  ✗ 加载传统特征失败: {e}")
        return None
    
    # 2. 加载DNA序列
    print("2. 加载DNA序列...")
    try:
        if not os.path.exists(seq_folder):
            print(f"  ✗ 序列文件夹不存在: {seq_folder}")
            return None
            
        sequences, seq_labels = load_dna_sequences_from_folder(seq_folder)
        
        if not sequences:
            print("  ✗ 序列加载失败")
            return None
        
        print(f"  ✓ 序列数量: {len(sequences)}")
        print(f"  ✓ 标签分布: 正样本={sum(seq_labels)}, 负样本={len(seq_labels)-sum(seq_labels)}")
    except Exception as e:
        print(f"  ✗ 加载序列失败: {e}")
        return None
    
    # 3. 数据对齐
    print("3. 数据对齐...")
    min_len = min(len(feature_data), len(sequences))
    feature_data = feature_data.iloc[:min_len]
    sequences = sequences[:min_len]
    seq_labels = seq_labels[:min_len]
    
    # 获取真实标签
    if '#class' in feature_data.columns:
        true_labels = feature_data['#class'].values
    else:
        true_labels = np.array(seq_labels)
    
    print(f"  ✓ 对齐后样本数: {min_len}")
    print(f"  ✓ 最终标签分布: 正样本={sum(true_labels)}, 负样本={len(true_labels)-sum(true_labels)}")
    
    # 4. 特征提取和组合
    print("4. 特征提取和组合...")
    
    # 提取序列特征
    seq_features = extract_sequence_features_simple(sequences)
    print(f"  ✓ 序列特征形状: {seq_features.shape}")
    
    # 处理传统特征
    traditional_features = feature_info['traditional_features']
    X_traditional = feature_data[traditional_features]
    X_traditional.replace('na', np.nan, inplace=True)
    X_traditional = X_traditional.astype(float, errors='ignore')
    print(f"  ✓ 传统特征形状: {X_traditional.shape}")
    
    # 组合特征
    seq_feature_names = feature_info['sequence_features']
    seq_df = pd.DataFrame(seq_features, columns=seq_feature_names, index=X_traditional.index)
    combined_features = pd.concat([X_traditional, seq_df], axis=1)
    print(f"  ✓ 组合特征形状: {combined_features.shape}")
    
    # 5. 预处理
    print("5. 数据预处理...")
    X_imputed = imputer.transform(combined_features)
    X_scaled = scaler.transform(X_imputed)
    print("  ✓ 预处理完成")
    
    # 6. 预测
    print("6. 进行预测...")
    predictions = model.predict_proba(X_scaled)[:, 1]
    print("  ✓ 预测完成")
    
    # 7. 评估
    print("7. 评估结果...")
    metrics = metricsScores(true_labels, predictions)
    
    metrics_names = ['Sen', 'Spe', 'Pre', 'F1', 'MCC', 'ACC', 'AUC', 'AUPR']
    
    print(f"\n{dataset_name} 结果:")
    print("=" * 50)
    for name, value in zip(metrics_names, metrics[:8]):
        print(f"  {name}: {value:.4f}")
    
    # 构建结果字典
    result = {
        'dataset_name': dataset_name,
        'n_samples': min_len,
        'label_distribution': {
            'positive': int(sum(true_labels)),
            'negative': int(len(true_labels) - sum(true_labels))
        },
        'metrics': dict(zip(metrics_names, metrics[:8])),
        'feature_info': {
            'traditional_features': len(traditional_features),
            'sequence_features': seq_features.shape[1],
            'total_features': combined_features.shape[1]
        },
        'files_used': {
            'feature_file': feature_file,
            'sequence_folder': seq_folder
        }
    }
    
    return result

def main():
    """主函数"""
    print("🧬 完整数据集评估 - 使用正确的DNA序列")
    print("=" * 60)
    
    # 1. 检查模型文件
    print("检查模型文件...")
    model_files = [
        './model/simple_combined_model.pkl',
        './model/simple_imputer.pkl',
        './model/simple_scaler.pkl',
        './model/simple_feature_info.json'
    ]
    
    missing_files = [f for f in model_files if not os.path.exists(f)]
    if missing_files:
        print("缺少以下模型文件:")
        for f in missing_files:
            print(f"  - {f}")
        print("请先运行 simple_train.py 进行训练")
        return 1
    
    # 2. 加载模型和预处理器
    print("加载模型和预处理器...")
    try:
        model = joblib.load('./model/simple_combined_model.pkl')
        imputer = joblib.load('./model/simple_imputer.pkl')
        scaler = joblib.load('./model/simple_scaler.pkl')
        
        with open('./model/simple_feature_info.json', 'r') as f:
            feature_info = json.load(f)
        
        print("✓ 模型加载成功")
    except Exception as e:
        print(f"✗ 模型加载失败: {e}")
        return 1
    
    # 3. 定义数据集配置
    datasets = [
        {
            'name': '训练集',
            'feature_file': './data/feature-encoded/for-train-test/train-closeby6-encoded-feat146.csv',
            'seq_folder': './data/train_seq_cosmic'
        },
        {
            'name': '测试集1',
            'feature_file': './data/feature-encoded/for-train-test/test1-closeby6-encoded-feat146.csv',
            'seq_folder': './data/test1_seq_cosmic'
        },
        {
            'name': '测试集2',
            'feature_file': './data/feature-encoded/for-train-test/test2-closeby6-encoded-feat146.csv',
            'seq_folder': './data/test2_seq_cosmic'
        }
    ]
    
    # 4. 评估所有数据集
    all_results = []
    
    for dataset in datasets:
        result = evaluate_dataset(
            dataset['name'],
            dataset['feature_file'],
            dataset['seq_folder'],
            model, imputer, scaler, feature_info
        )
        
        if result:
            all_results.append(result)
    
    # 5. 保存结果
    if all_results:
        print(f"\n=== 保存完整评估结果 ===")
        os.makedirs('./results', exist_ok=True)
        
        # 保存详细结果
        with open('./results/complete_evaluation_results.json', 'w') as f:
            json.dump(all_results, f, indent=2, ensure_ascii=False)
        
        # 保存CSV摘要
        summary_data = []
        for result in all_results:
            row = {
                'dataset': result['dataset_name'],
                'n_samples': result['n_samples'],
                'positive': result['label_distribution']['positive'],
                'negative': result['label_distribution']['negative']
            }
            row.update(result['metrics'])
            summary_data.append(row)
        
        df_summary = pd.DataFrame(summary_data)
        df_summary.to_csv('./results/complete_evaluation_summary.csv', index=False)
        
        print("✓ 结果已保存到:")
        print("  - ./results/complete_evaluation_results.json")
        print("  - ./results/complete_evaluation_summary.csv")
        
        # 6. 显示汇总结果
        print(f"\n=== 完整评估结果汇总 ===")
        print("=" * 80)
        print(f"{'数据集':<10} {'样本数':<8} {'正样本':<8} {'负样本':<8} {'AUC':<8} {'AUPR':<8} {'ACC':<8} {'F1':<8}")
        print("-" * 80)
        
        for result in all_results:
            print(f"{result['dataset_name']:<10} "
                  f"{result['n_samples']:<8} "
                  f"{result['label_distribution']['positive']:<8} "
                  f"{result['label_distribution']['negative']:<8} "
                  f"{result['metrics']['AUC']:<8.4f} "
                  f"{result['metrics']['AUPR']:<8.4f} "
                  f"{result['metrics']['ACC']:<8.4f} "
                  f"{result['metrics']['F1']:<8.4f}")
        
        print("\n🎉 完整评估完成!")
        print("现在您有了训练集、测试集1和测试集2的真实性能指标")
        
        return 0
    else:
        print("\n❌ 评估失败")
        return 1

if __name__ == "__main__":
    exit(main())
