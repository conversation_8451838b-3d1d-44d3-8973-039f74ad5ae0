{"model_name": "soft_ensemble_xgb_rf_feat73_gpu", "model_path": "model/soft_ensemble_xgb_rf_feat73_gpu.model", "feature_count": 73, "training_samples": 3494, "performance_metrics": {"xgb_auc": 0.6037822349570201, "rf_auc": 0.6431764224314367, "ensemble_auc": 0.6400245599672534, "weights": [0.1, 0.9], "xgb_cv_auc_mean": 0.6724199003761405, "xgb_cv_auc_std": 0.03365972845067665, "rf_cv_auc_mean": 0.6872449956427756, "rf_cv_auc_std": 0.03755635449564372, "feature_count": 73, "training_samples": 3494}, "ensemble_weights": {"xgboost": 0.1, "random_forest": 0.9}, "use_gpu": true, "features_used": ["MACIE01", "MACIE10", "MACIE00", "MACIE11", "MACIE_conserved", "MACIE_regulatory", "MACIE_anyclass", "FunSeq_Score", "GenoCanyon_Score", "FIRE_Score", "ReMM_Score", "RegSeq0", "RegSeq1", "RegSeq2", "RegSeq3", "RegSeq4", "RegSeq5", "RegSeq6", "RegSeq7", "SpliceAI-acc-gain", "SpliceAI-acc-loss", "SpliceAI-don-gain", "SpliceAI-don-loss", "MMSp_acceptor", "MMSp_exon", "MMSp_donor", "siPhy_rankscore", "integrated_fitCons_score", "integrated_confidence_value", "GM12878_fitCons_score", "GM12878_confidence_value", "H1-hESC_fitCons_score", "H1-hESC_confidence_value", "HUVEC_fitCons_score", "HUVEC_confidence_value", "integrated_fitCons_score_rankscore", "GM12878_fitCons_score_rankscore", "H1-hESC_fitCons_score_rankscore", "HUVEC_fitCons_score_rankscore", "priPhCons", "mamPhCons", "verPhCons", "priPhyloP", "mamPhyloP", "verPhyloP", "bStatistic", "GerpRS", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "GerpN", "GerpS", "ZooPriPhyloP", "ZooVerPhyloP", "EncodeH3K4me1-sum", "EncodeH3K4me1-max", "EncodeH3K4me2-sum", "EncodeH3K4me2-max", "EncodeH3K4me3-sum", "EncodeH3K4me3-max", "EncodeH3K9ac-sum", "EncodeH3K9ac-max", "EncodeH3K9me3-sum", "EncodeH3K9me3-max", "EncodeH3K27ac-sum", "EncodeH3K27ac-max", "EncodeH3K27me3-sum", "EncodeH3K27me3-max", "EncodeH3K36me3-sum", "EncodeH3K36me3-max", "EncodeH3K79me2-sum", "EncodeH3K79me2-max", "EncodeH4K20me1-sum", "EncodeH4K20me1-max", "EncodeH2AFZ-sum"]}