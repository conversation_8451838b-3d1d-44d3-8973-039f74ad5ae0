[{"dataset": "训练集", "n_samples": 3494, "n_positive": 1747, "n_negative": 1747, "metrics": {"Sen": 1.0, "Spe": 1.0, "Pre": 1.0, "F1": 1.0, "MCC": 1.0, "ACC": 1.0, "AUC": 1.0, "AUPR": 1.0}, "cv_auc_mean": 0.8895105552734144, "cv_auc_std": 0.007345071340982406, "best_params": {"objective": "binary:logistic", "base_score": null, "booster": null, "callbacks": null, "colsample_bylevel": null, "colsample_bynode": null, "colsample_bytree": 0.9, "device": null, "early_stopping_rounds": null, "enable_categorical": false, "eval_metric": "logloss", "feature_types": null, "gamma": null, "grow_policy": null, "importance_type": null, "interaction_constraints": null, "learning_rate": 0.1, "max_bin": null, "max_cat_threshold": null, "max_cat_to_onehot": null, "max_delta_step": null, "max_depth": 6, "max_leaves": null, "min_child_weight": null, "missing": NaN, "monotone_constraints": null, "multi_strategy": null, "n_estimators": 200, "n_jobs": -1, "num_parallel_tree": null, "random_state": 42, "reg_alpha": null, "reg_lambda": null, "sampling_method": null, "scale_pos_weight": null, "subsample": 0.9, "tree_method": null, "validate_parameters": null, "verbosity": null}}, {"dataset": "测试集1", "n_samples": 6650, "n_positive": 3325, "n_negative": 3325, "metrics": {"Sen": 0.7795488721804511, "Spe": 0.8273684210526315, "Pre": 0.8186986734049273, "F1": 0.7986442766908026, "MCC": 0.607612407298869, "ACC": 0.8034586466165413, "AUC": 0.8708365198711063, "AUPR": 0.8896919879617202}, "feature_alignment": "matched"}, {"dataset": "测试集2", "n_samples": 1960, "n_positive": 980, "n_negative": 980, "metrics": {"Sen": 0.9326530612244898, "Spe": 0.5979591836734693, "Pre": 0.6987767584097859, "F1": 0.798951048951049, "MCC": 0.5630872241521196, "ACC": 0.7653061224489796, "AUC": 0.8472672844648064, "AUPR": 0.8162751990197322}, "feature_alignment": "matched"}]