import pandas as pd
import numpy as np
import json
import os
from utils import rename_with_feat40, metricsScores
from train_cnn_xgb_ensemble import CNNXGBEnsemble
import warnings
warnings.filterwarnings("ignore")

def load_test_sequences(test_file_prefix):
    """
    加载测试序列数据
    
    Args:
        test_file_prefix: 测试文件前缀 (如 'test1' 或 'test2')
        
    Returns:
        sequences: 序列列表
        labels: 标签列表
    """
    sequences = []
    labels = []
    
    # 构建文件路径
    pos_file = f'./data/seq_cosmic/{test_file_prefix}_pos_seq.txt'
    neg_file = f'./data/seq_cosmic/{test_file_prefix}_neg_seq.txt'
    
    # 检查文件是否存在
    if not os.path.exists(pos_file) or not os.path.exists(neg_file):
        print(f"警告: 测试序列文件不存在，将使用训练序列文件进行演示")
        pos_file = './data/seq_cosmic/train_pos_seq.txt'
        neg_file = './data/seq_cosmic/train_neg_seq.txt'
    
    # 读取正样本序列
    if os.path.exists(pos_file):
        with open(pos_file, 'r') as f:
            for line in f:
                parts = line.strip().split('\t')
                if len(parts) >= 5:
                    seq = parts[4]
                    sequences.append(seq)
                    labels.append(1)
    
    # 读取负样本序列
    if os.path.exists(neg_file):
        with open(neg_file, 'r') as f:
            for line in f:
                parts = line.strip().split('\t')
                if len(parts) >= 5:
                    seq = parts[4]
                    sequences.append(seq)
                    labels.append(0)
    
    return sequences, labels

def test_cnn_xgb_ensemble():
    """测试CNN+XGBoost集成模型"""
    
    print("=== CNN+XGBoost集成模型测试 ===")
    
    # 1. 初始化模型
    print("1. 初始化模型...")
    ensemble = CNNXGBEnsemble(cnn_feature_dim=128, use_gpu=True)
    
    # 2. 加载训练好的模型
    print("2. 加载训练好的模型...")
    try:
        ensemble.load_trained_model('./model')
        print("模型加载成功")
    except Exception as e:
        print(f"模型加载失败: {e}")
        print("请先运行训练脚本 train_cnn_xgb_ensemble.py")
        return
    
    # 3. 测试集1
    print("\n3. 测试集1评估...")
    test1_results = evaluate_test_set(ensemble, 'test1')
    
    # 4. 测试集2 (如果存在)
    print("\n4. 测试集2评估...")
    test2_results = evaluate_test_set(ensemble, 'test2')
    
    # 5. 保存结果
    print("\n5. 保存测试结果...")
    save_test_results(test1_results, test2_results)
    
    print("=== 测试完成 ===")

def evaluate_test_set(ensemble, test_prefix):
    """
    评估测试集
    
    Args:
        ensemble: 集成模型
        test_prefix: 测试集前缀
        
    Returns:
        results: 评估结果
    """
    try:
        # 加载传统特征数据
        test_file = f'./data/feature-encoded/for-train-test/{test_prefix}-closeby6-encoded-feat146.csv'
        
        if not os.path.exists(test_file):
            print(f"警告: {test_file} 不存在，跳过{test_prefix}测试")
            return None
        
        test_data, _ = rename_with_feat40(test_file)
        print(f"{test_prefix}传统特征数据形状: {test_data.shape}")
        
        # 加载序列数据
        sequences, seq_labels = load_test_sequences(test_prefix)
        print(f"{test_prefix}序列数据: {len(sequences)} 个序列")
        
        # 获取真实标签
        if '#class' in test_data.columns:
            true_labels = test_data['#class'].values
        else:
            true_labels = np.array(seq_labels)
        
        # 确保数据长度一致
        min_len = min(len(test_data), len(sequences), len(true_labels))
        test_data = test_data.iloc[:min_len]
        sequences = sequences[:min_len]
        true_labels = true_labels[:min_len]
        
        print(f"最终测试样本数: {min_len}")
        print(f"标签分布: {np.bincount(true_labels)}")
        
        # 预测
        print("进行预测...")
        predictions = ensemble.predict(test_data, sequences)
        
        # 计算评估指标
        metrics = metricsScores(true_labels, predictions)
        
        # 输出结果
        metrics_names = ['Sen', 'Spe', 'Pre', 'F1', 'MCC', 'ACC', 'AUC', 'AUPR']
        print(f"\n{test_prefix.upper()}测试集结果:")
        for name, value in zip(metrics_names, metrics[:8]):
            print(f"  {name}: {value:.4f}")
        
        # 构建结果字典
        results = {
            'test_set': test_prefix,
            'n_samples': min_len,
            'label_distribution': np.bincount(true_labels).tolist(),
            'metrics': dict(zip(metrics_names, metrics[:8])),
            'predictions': predictions.tolist(),
            'true_labels': true_labels.tolist()
        }
        
        return results
        
    except Exception as e:
        print(f"评估{test_prefix}时出错: {e}")
        return None

def save_test_results(test1_results, test2_results):
    """
    保存测试结果
    
    Args:
        test1_results: 测试集1结果
        test2_results: 测试集2结果
    """
    # 创建结果目录
    os.makedirs('./results', exist_ok=True)
    
    # 保存详细结果
    all_results = {
        'model_type': 'CNN+XGBoost Ensemble',
        'test1_results': test1_results,
        'test2_results': test2_results
    }
    
    with open('./results/cnn_xgb_ensemble_test_results.json', 'w') as f:
        json.dump(all_results, f, indent=2)
    
    # 保存简化的CSV结果
    results_summary = []
    
    if test1_results:
        row = {'test_set': 'test1'}
        row.update(test1_results['metrics'])
        row['n_samples'] = test1_results['n_samples']
        results_summary.append(row)
    
    if test2_results:
        row = {'test_set': 'test2'}
        row.update(test2_results['metrics'])
        row['n_samples'] = test2_results['n_samples']
        results_summary.append(row)
    
    if results_summary:
        df_results = pd.DataFrame(results_summary)
        df_results.to_csv('./results/cnn_xgb_ensemble_summary.csv', index=False)
        print("结果已保存到:")
        print("  - ./results/cnn_xgb_ensemble_test_results.json")
        print("  - ./results/cnn_xgb_ensemble_summary.csv")

def compare_with_original():
    """与原始模型进行比较"""
    print("\n=== 与原始模型比较 ===")
    
    # 读取原始模型结果
    original_files = [
        './results/test1_xgb_rf_ada_results.csv',
        './results/test2_xgb_rf_ada_results.csv'
    ]
    
    cnn_xgb_file = './results/cnn_xgb_ensemble_summary.csv'
    
    if os.path.exists(cnn_xgb_file):
        cnn_xgb_results = pd.read_csv(cnn_xgb_file)
        
        for original_file in original_files:
            if os.path.exists(original_file):
                original_results = pd.read_csv(original_file)
                test_name = 'test1' if 'test1' in original_file else 'test2'
                
                print(f"\n{test_name.upper()}比较:")
                
                # 找到对应的CNN+XGBoost结果
                cnn_row = cnn_xgb_results[cnn_xgb_results['test_set'] == test_name]
                
                if not cnn_row.empty and not original_results.empty:
                    metrics_to_compare = ['AUC', 'AUPR', 'ACC', 'F1', 'MCC']
                    
                    for metric in metrics_to_compare:
                        if metric in original_results.columns and metric in cnn_row.columns:
                            original_val = original_results[metric].iloc[0]
                            cnn_val = cnn_row[metric].iloc[0]
                            improvement = cnn_val - original_val
                            
                            print(f"  {metric}:")
                            print(f"    原始模型: {original_val:.4f}")
                            print(f"    CNN+XGBoost: {cnn_val:.4f}")
                            print(f"    提升: {improvement:+.4f}")

def main():
    """主函数"""
    # 测试集成模型
    test_cnn_xgb_ensemble()
    
    # 与原始模型比较
    compare_with_original()

if __name__ == "__main__":
    main()
