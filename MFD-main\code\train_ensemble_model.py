import pandas as pd
import numpy as np
import joblib
import json
from sklearn.ensemble import RandomForestClassifier, VotingClassifier, StackingClassifier
from xgboost import XGBClassifier
from sklearn.model_selection import GridSearchCV, StratifiedKFold
from sklearn.preprocessing import MinMaxScaler
from sklearn.impute import SimpleImputer
from sklearn.metrics import roc_auc_score, classification_report
from utils import rename_with_feat40
import warnings
warnings.filterwarnings("ignore")

def check_gpu_availability():
    """检查GPU是否可用"""
    try:
        import xgboost as xgb
        # 检查XGBoost是否支持GPU
        gpu_available = xgb.XGBClassifier(tree_method='gpu_hist', gpu_id=0)
        print("GPU可用，将使用GPU加速训练")
        return True
    except Exception as e:
        print(f"GPU不可用，使用CPU训练: {e}")
        return False

def train_ensemble_model():
    """训练XGBoost和随机森林的集成模型"""
    
    # 检查GPU可用性
    use_gpu = check_gpu_availability()
    
    # 1. 读取73维最优特征名称
    feat146_cols = pd.read_csv('./results/AAAA_featureSelect/feat_importance_sorted_rename.csv')
    feat73_cols = feat146_cols['feat_name'][:73].tolist()
    print(f"使用的73个最优特征: {len(feat73_cols)}")
    
    # 2. 读取并重命名训练数据
    train_data, renamed_columns = rename_with_feat40('./data/feature-encoded/for-train-test/train-closeby6-encoded-feat146.csv')
    print(f"训练数据形状: {train_data.shape}")
    
    # 3. 检查特征是否存在
    missing_features = [feat for feat in feat73_cols if feat not in train_data.columns]
    if missing_features:
        print(f"缺失的特征: {missing_features[:5]}...")
        available_features = [feat for feat in feat73_cols if feat in train_data.columns]
        print(f"可用特征数量: {len(available_features)}")
        feat73_cols = available_features
    
    # 4. 准备特征和标签
    if 'label' in train_data.columns:
        label_col = 'label'
    elif '#class' in train_data.columns:
        label_col = '#class'
    else:
        label_col = train_data.columns[-1]
        print(f"使用最后一列作为标签: {label_col}")
    
    X = train_data[feat73_cols]
    y = train_data[label_col]
    
    # 5. 数据预处理
    X.replace('na', np.nan, inplace=True)
    X = X.astype(float, errors='ignore')
    
    imputer = SimpleImputer(strategy='median')
    X_imputed = imputer.fit_transform(X)
    
    scaler = MinMaxScaler()
    X_scaled = scaler.fit_transform(X_imputed)
    X_final = pd.DataFrame(X_scaled, columns=feat73_cols)
    
    print(f"最终训练数据形状: {X_final.shape}")
    print(f"标签分布: {y.value_counts()}")
    
    # 6. 定义基础模型（GPU加速版本）
    if use_gpu:
        xgb_model = XGBClassifier(
            n_estimators=1000,
            learning_rate=0.1,
            max_depth=6,
            tree_method='gpu_hist',  # GPU加速
            gpu_id=0,               # 使用第一个GPU
            random_state=42,
            n_jobs=1                # GPU模式下设为1
        )
    else:
        xgb_model = XGBClassifier(
            n_estimators=1000,
            learning_rate=0.1,
            max_depth=6,
            random_state=42,
            n_jobs=-1
        )
    
    # 随机森林（CPU多线程）
    rf_model = RandomForestClassifier(
        n_estimators=1000,
        max_depth=10,
        min_samples_split=5,
        min_samples_leaf=2,
        random_state=42,
        n_jobs=-1  # 使用所有CPU核心
    )
    
    # 7. 方法1: Voting Classifier
    print("训练Voting Classifier...")
    voting_clf = VotingClassifier(
        estimators=[
            ('xgb', xgb_model),
            ('rf', rf_model)
        ],
        voting='soft',
        n_jobs=1 if use_gpu else -1  # GPU模式下避免并行冲突
    )
    
    # 参数搜索（GPU优化版本）
    if use_gpu:
        voting_params = {
            'xgb__n_estimators': [500, 1000],
            'xgb__learning_rate': [0.1, 0.2],
            'xgb__max_depth': [6, 8],
            'rf__n_estimators': [500, 1000],
            'rf__max_depth': [8, 10]
        }
    else:
        voting_params = {
            'xgb__n_estimators': [500, 1000],
            'xgb__learning_rate': [0.1, 0.2],
            'rf__n_estimators': [500, 1000],
            'rf__max_depth': [8, 10]
        }
    
    cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
    voting_grid = GridSearchCV(
        voting_clf, 
        param_grid=voting_params,
        scoring='roc_auc',
        cv=cv,
        verbose=2,  # 增加详细输出
        n_jobs=1 if use_gpu else -1
    )
    
    print("开始Voting Classifier训练...")
    voting_grid.fit(X_final, y)
    print(f"Voting Classifier最佳参数: {voting_grid.best_params_}")
    print(f"Voting Classifier最佳AUC: {voting_grid.best_score_:.4f}")
    
    # 8. 方法2: Stacking Classifier
    print("训练Stacking Classifier...")
    from sklearn.linear_model import LogisticRegression
    
    stacking_clf = StackingClassifier(
        estimators=[
            ('xgb', xgb_model),
            ('rf', rf_model)
        ],
        final_estimator=LogisticRegression(random_state=42),
        cv=5,
        n_jobs=1 if use_gpu else -1
    )
    
    stacking_params = {
        'xgb__n_estimators': [500, 1000],
        'xgb__learning_rate': [0.1, 0.2],
        'rf__n_estimators': [500, 1000],
        'rf__max_depth': [8, 10]
    }
    
    stacking_grid = GridSearchCV(
        stacking_clf,
        param_grid=stacking_params,
        scoring='roc_auc',
        cv=cv,
        verbose=2,
        n_jobs=1 if use_gpu else -1
    )
    
    print("开始Stacking Classifier训练...")
    stacking_grid.fit(X_final, y)
    print(f"Stacking Classifier最佳参数: {stacking_grid.best_params_}")
    print(f"Stacking Classifier最佳AUC: {stacking_grid.best_score_:.4f}")
    
    # 9. 选择最佳模型并保存
    if voting_grid.best_score_ > stacking_grid.best_score_:
        best_model = voting_grid.best_estimator_
        model_name = "voting_xgb_rf_feat73_gpu.model" if use_gpu else "voting_xgb_rf_feat73.model"
        print(f"选择Voting Classifier作为最终模型 (AUC: {voting_grid.best_score_:.4f})")
    else:
        best_model = stacking_grid.best_estimator_
        model_name = "stacking_xgb_rf_feat73_gpu.model" if use_gpu else "stacking_xgb_rf_feat73.model"
        print(f"选择Stacking Classifier作为最终模型 (AUC: {stacking_grid.best_score_:.4f})")
    
    # 10. 保存模型和预处理器
    joblib.dump(best_model, f'./model/{model_name}')
    joblib.dump(imputer, './model/ensemble_imputer.pkl')
    joblib.dump(scaler, './model/ensemble_minmax.pkl')
    
    with open('./model/feat73_names.json', 'w') as f:
        json.dump(feat73_cols, f)
    
    print(f"模型已保存: ./model/{model_name}")
    print(f"使用{'GPU' if use_gpu else 'CPU'}训练完成")
    print(f"实际使用特征数量: {len(feat73_cols)}")
    
    return best_model, imputer, scaler, feat73_cols

if __name__ == "__main__":
    model, imputer, scaler, features = train_ensemble_model()

