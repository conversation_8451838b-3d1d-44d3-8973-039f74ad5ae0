import pandas as pd
import numpy as np
import json
from sklearn.model_selection import cross_val_predict, StratifiedKFold
from sklearn.ensemble import VotingClassifier, RandomForestClassifier
from xgboost import XGBClassifier
from sklearn.preprocessing import MinMaxScaler
from sklearn.impute import SimpleImputer
from utils import rename_with_feat40, metricsScores
import warnings
warnings.filterwarnings("ignore")

print("=== 从头训练模型进行交叉验证 ===")

# 加载数据
feat146_cols = pd.read_csv(r'./results/AAAA_featureSelect/feat_importance_sorted_rename.csv')
feat73_cols = feat146_cols['feat_name'][:73].tolist()

with open('./data/feat146.json', 'r') as f:
    feat146 = json.load(f)

X_train, _ = rename_with_feat40(r'./data/feature-encoded/for-train-test/')
print(f"数据形状: {X_train.shape}")

# 数据预处理
X_features = X_train[feat146].copy()
X_features.replace('na', np.nan, inplace=True)
y_true = X_train['#class'].values

# 重新训练预处理器
imputer = SimpleImputer(strategy='median')
scaler = MinMaxScaler()

X_imputed = imputer.fit_transform(X_features)
X_scaled = scaler.fit_transform(X_imputed)
X_final = pd.DataFrame(X_scaled, columns=feat146)

# 选择73个特征
X_selected = X_final[feat73_cols]

print(f"特征形状: {X_selected.shape}")
print(f"标签分布: {np.bincount(y_true)}")

# 创建新的模型（未训练）
model = VotingClassifier([
    ('xgb', XGBClassifier(random_state=42, n_estimators=500)),
    ('rf', RandomForestClassifier(random_state=42, n_estimators=500))
], voting='soft')

# 交叉验证预测
print("进行5折交叉验证...")
cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
pred_proba = cross_val_predict(model, X_selected.values, y_true, 
                              cv=cv, method='predict_proba')[:, 1]

print(f"预测概率范围: [{pred_proba.min():.4f}, {pred_proba.max():.4f}]")
print(f"预测概率均值: {pred_proba.mean():.4f}")

# 计算指标
metrics = 'Sen, Spe, Pre, F1, MCC, ACC, AUC, AUPR, tn, fp, fn, tp, thres'.split(', ')
scores = metricsScores(y_true, pred_proba)

result_df = pd.DataFrame([scores], columns=metrics)
print("\n交叉验证结果:")
print(result_df.iloc[:, :-5])  # 不显示混淆矩阵