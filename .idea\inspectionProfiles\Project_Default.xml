<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="23">
            <item index="0" class="java.lang.String" itemvalue="Bio" />
            <item index="1" class="java.lang.String" itemvalue="torchmetrics" />
            <item index="2" class="java.lang.String" itemvalue="transformers" />
            <item index="3" class="java.lang.String" itemvalue="scikit-learn" />
            <item index="4" class="java.lang.String" itemvalue="torch" />
            <item index="5" class="java.lang.String" itemvalue="numpy" />
            <item index="6" class="java.lang.String" itemvalue="torchvision" />
            <item index="7" class="java.lang.String" itemvalue="ipykernel" />
            <item index="8" class="java.lang.String" itemvalue="pandas" />
            <item index="9" class="java.lang.String" itemvalue="pytorch-lightning" />
            <item index="10" class="java.lang.String" itemvalue="autogluon" />
            <item index="11" class="java.lang.String" itemvalue="seqeval" />
            <item index="12" class="java.lang.String" itemvalue="pytorch-nlp" />
            <item index="13" class="java.lang.String" itemvalue="accelerate" />
            <item index="14" class="java.lang.String" itemvalue="fpocket" />
            <item index="15" class="java.lang.String" itemvalue="biopython" />
            <item index="16" class="java.lang.String" itemvalue="bertviz" />
            <item index="17" class="java.lang.String" itemvalue="jupyterlab" />
            <item index="18" class="java.lang.String" itemvalue="xgboost" />
            <item index="19" class="java.lang.String" itemvalue="torchaudio" />
            <item index="20" class="java.lang.String" itemvalue="ipywidgets" />
            <item index="21" class="java.lang.String" itemvalue="seaborn" />
            <item index="22" class="java.lang.String" itemvalue="rtviz" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>