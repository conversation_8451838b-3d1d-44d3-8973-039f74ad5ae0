#!/usr/bin/env python3
"""
CNN+XGBoost集成模型完整流水线脚本
自动化执行训练、测试和可视化的完整流程
"""

import os
import sys
import subprocess
import time
import argparse
from pathlib import Path

def check_requirements():
    """检查环境和依赖"""
    print("=== 检查环境和依赖 ===")
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("错误: 需要Python 3.7或更高版本")
        return False
    
    print(f"Python版本: {sys.version}")
    
    # 检查必要的包
    required_packages = [
        'torch', 'numpy', 'pandas', 'scikit-learn', 
        'xgboost', 'matplotlib', 'seaborn', 'joblib'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package}")
    
    if missing_packages:
        print(f"\n缺少以下包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    return True

def check_data_files():
    """检查数据文件"""
    print("\n=== 检查数据文件 ===")
    
    required_files = [
        './data/feature-encoded/for-train-test/train-closeby6-encoded-feat146.csv',
        './data/feat146.json',
        './data/seq_cosmic/train_pos_seq.txt',
        './data/seq_cosmic/train_neg_seq.txt'
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}")
        else:
            missing_files.append(file_path)
            print(f"✗ {file_path}")
    
    if missing_files:
        print(f"\n缺少以下数据文件:")
        for f in missing_files:
            print(f"  - {f}")
        return False
    
    return True

def run_demo():
    """运行演示"""
    print("\n=== 运行快速演示 ===")
    try:
        result = subprocess.run([
            sys.executable, 'code/cnn_xgb_demo.py'
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✓ 演示运行成功")
            print(result.stdout)
            return True
        else:
            print("✗ 演示运行失败")
            print(result.stderr)
            return False
    except subprocess.TimeoutExpired:
        print("✗ 演示运行超时")
        return False
    except Exception as e:
        print(f"✗ 演示运行出错: {e}")
        return False

def run_training(epochs=30, gpu=True):
    """运行训练"""
    print(f"\n=== 开始训练 (epochs={epochs}, gpu={gpu}) ===")
    
    # 创建模型目录
    os.makedirs('./model', exist_ok=True)
    
    try:
        # 修改训练脚本中的参数
        training_script = """
import sys
sys.path.append('.')
from code.train_cnn_xgb_ensemble import CNNXGBEnsemble

def main():
    ensemble = CNNXGBEnsemble(cnn_feature_dim=128, use_gpu={gpu})
    
    training_report = ensemble.train(
        train_file='./data/feature-encoded/for-train-test/train-closeby6-encoded-feat146.csv',
        feat_names_file='./data/feat146.json',
        pos_seq_file='./data/seq_cosmic/train_pos_seq.txt',
        neg_seq_file='./data/seq_cosmic/train_neg_seq.txt',
        test_size=0.2,
        cnn_epochs={epochs}
    )
    
    print("训练完成!")
    return training_report

if __name__ == "__main__":
    main()
""".format(epochs=epochs, gpu=gpu)
        
        # 写入临时训练脚本
        with open('temp_train.py', 'w') as f:
            f.write(training_script)
        
        # 运行训练
        start_time = time.time()
        result = subprocess.run([
            sys.executable, 'temp_train.py'
        ], timeout=3600)  # 1小时超时
        
        end_time = time.time()
        training_time = end_time - start_time
        
        # 清理临时文件
        if os.path.exists('temp_train.py'):
            os.remove('temp_train.py')
        
        if result.returncode == 0:
            print(f"✓ 训练完成 (耗时: {training_time:.1f}秒)")
            return True
        else:
            print("✗ 训练失败")
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ 训练超时")
        return False
    except Exception as e:
        print(f"✗ 训练出错: {e}")
        return False

def run_testing():
    """运行测试"""
    print("\n=== 开始测试 ===")
    
    try:
        result = subprocess.run([
            sys.executable, 'code/test_cnn_xgb_ensemble.py'
        ], capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print("✓ 测试完成")
            print(result.stdout)
            return True
        else:
            print("✗ 测试失败")
            print(result.stderr)
            return False
    except subprocess.TimeoutExpired:
        print("✗ 测试超时")
        return False
    except Exception as e:
        print(f"✗ 测试出错: {e}")
        return False

def run_visualization():
    """运行可视化"""
    print("\n=== 生成可视化结果 ===")
    
    try:
        result = subprocess.run([
            sys.executable, 'code/visualize_cnn_xgb_results.py'
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✓ 可视化完成")
            return True
        else:
            print("✗ 可视化失败")
            print(result.stderr)
            return False
    except subprocess.TimeoutExpired:
        print("✗ 可视化超时")
        return False
    except Exception as e:
        print(f"✗ 可视化出错: {e}")
        return False

def print_results_summary():
    """打印结果摘要"""
    print("\n=== 结果摘要 ===")
    
    # 检查生成的文件
    result_files = [
        './model/dna_cnn_model.pth',
        './model/cnn_xgb_ensemble_model.pkl',
        './model/cnn_xgb_training_report.json',
        './results/cnn_xgb_ensemble_summary.csv',
        './results/plots/feature_importance_analysis.png',
        './results/plots/performance_comparison.png'
    ]
    
    print("生成的文件:")
    for file_path in result_files:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"  ✓ {file_path} ({size} bytes)")
        else:
            print(f"  ✗ {file_path}")
    
    # 读取并显示性能结果
    summary_file = './results/cnn_xgb_ensemble_summary.csv'
    if os.path.exists(summary_file):
        import pandas as pd
        try:
            results = pd.read_csv(summary_file)
            print(f"\n性能结果:")
            print(results.to_string(index=False))
        except Exception as e:
            print(f"读取结果文件时出错: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='CNN+XGBoost集成模型完整流水线')
    parser.add_argument('--mode', choices=['demo', 'train', 'test', 'visualize', 'full'], 
                       default='full', help='运行模式')
    parser.add_argument('--epochs', type=int, default=30, help='CNN训练轮数')
    parser.add_argument('--no-gpu', action='store_true', help='不使用GPU')
    parser.add_argument('--skip-checks', action='store_true', help='跳过环境检查')
    
    args = parser.parse_args()
    
    print("🧬 CNN+XGBoost集成模型流水线")
    print("=" * 50)
    
    # 环境检查
    if not args.skip_checks:
        if not check_requirements():
            print("环境检查失败，退出")
            return 1
        
        if not check_data_files():
            print("数据文件检查失败，退出")
            return 1
    
    use_gpu = not args.no_gpu
    success = True
    
    # 根据模式运行不同步骤
    if args.mode == 'demo':
        success = run_demo()
    
    elif args.mode == 'train':
        success = run_training(args.epochs, use_gpu)
    
    elif args.mode == 'test':
        success = run_testing()
    
    elif args.mode == 'visualize':
        success = run_visualization()
    
    elif args.mode == 'full':
        # 完整流程
        steps = [
            ("演示", lambda: run_demo()),
            ("训练", lambda: run_training(args.epochs, use_gpu)),
            ("测试", lambda: run_testing()),
            ("可视化", lambda: run_visualization())
        ]
        
        for step_name, step_func in steps:
            print(f"\n{'='*20} {step_name} {'='*20}")
            if not step_func():
                print(f"{step_name}失败，停止流程")
                success = False
                break
            time.sleep(2)  # 短暂暂停
    
    # 打印最终结果
    if success:
        print_results_summary()
        print(f"\n🎉 流程完成! 模式: {args.mode}")
        print("查看 ./results/ 目录获取详细结果")
        return 0
    else:
        print(f"\n❌ 流程失败! 模式: {args.mode}")
        return 1

if __name__ == "__main__":
    exit(main())
