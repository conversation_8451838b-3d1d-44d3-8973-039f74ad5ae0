# train_xgb_rf.py
import numpy as np
from utils import readData, params_search, delete70NaN, fillMeanMaxMin

def main():
    print("[1] 加载并预处理数据...")
    X_train, Y_train, _, _, _, _ = readData(test=False, save=False)  # 不加载测试集

    print("[2] 删除缺失值>70%的特征...")
    X_train_clean, cols_remain = delete70NaN(X_train)
    X_train_clean = np.array(X_train_clean).astype(float)
    Y_train = np.array(Y_train).astype(int)

    print("[3] 缺失值填充 + 归一化...")
    X_train_final = fillMeanMaxMin(X_train_clean)

    print("[4] 训练 XGBoost...")
    params_search(X_train_final, Y_train, method='xgboost')

    print("[5] 训练 RandomForest...")
    params_search(X_train_final, Y_train, method='rfc')

    print("✅ 模型已保存到 ./model/ 目录。")

if __name__ == '__main__':
    main()