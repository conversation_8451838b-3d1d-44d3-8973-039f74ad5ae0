import pandas as pd
import numpy as np
import joblib, json
from utils import rename_with_feat40, metricsScores
from sklearn.preprocessing import MinMaxScaler
import warnings
warnings.filterwarnings("ignore")

# 提取最优特征子集名称
feat146_cols = pd.read_csv(r'./results/AAAA_featureSelect/feat_importance_sorted_rename.csv')
feat73_cols = feat146_cols['feat_name'][:73]

# 读取特征名称
with open('./data/feat146.json', 'r') as f:
    feat146 = json.load(f)

# 加载最优模型和预处理器
model = joblib.load(r'./model/voting_xgb_rf_feat73_gpu.model')
imputer = joblib.load('./model/imputer.pkl')
minmax = joblib.load('./model/minmax.pkl')

# 读取数据
metrics = 'Sen, Spe, Pre, F1, MCC, ACC, AUC, AUPR, tn, fp, fn, tp, thres'.split(', ')

# 训练集预测
print("处理训练集数据...")
X_train, renamed_columns = rename_with_feat40(r'./data/feature-encoded/for-train-test/train-closeby6-encoded-feat146.csv')
print(f"训练集数据形状: {X_train.shape}")
print(f"训练集列名: {X_train.columns.tolist()}")

# 数据预处理
X_train[feat146].replace('na', np.nan, inplace=True)
X_train_processed = imputer.transform(X_train[feat146])
X_train_processed = minmax.transform(X_train_processed)
X_train_processed = pd.DataFrame(X_train_processed, columns=feat146)

# 模型预测
pred_train = model.predict_proba(X_train_processed.loc[:, feat73_cols].values)[:, 1]

# 计算评估指标
result_df_train = pd.concat([
    pd.Series(metrics), 
    pd.Series(metricsScores(X_train['#class'].values, pred_train))
], axis=1).T

result_df_train.columns = result_df_train.iloc[0]  # 将第一行设置为列标题
result_df_train = result_df_train[1:]  # 去掉原来的第一行

print("训练集预测结果:")
print(result_df_train.iloc[:, :-5])  # 不显示混淆矩阵部分

# 可选：保存结果
result_df_train.to_csv('./results/train_prediction_results.csv', index=False)
print("结果已保存到 ./results/train_prediction_results.csv")