#!/usr/bin/env python3
"""
修正版测试脚本 - 正确处理缺少测试序列的情况
"""

import sys
import os
import pandas as pd
import numpy as np
import json
import joblib

# 添加code目录到路径
sys.path.append('./code')
sys.path.append('.')

try:
    from utils import rename_with_feat40, metricsScores
    print("✓ 成功导入utils模块")
except ImportError as e:
    print(f"✗ 导入utils失败: {e}")
    sys.exit(1)

def test_traditional_features_only():
    """仅使用传统特征进行测试（作为对比基准）"""
    print("\n=== 仅传统特征测试 ===")
    
    # 1. 加载模型和预处理器
    print("1. 加载训练好的模型...")
    try:
        model = joblib.load('./model/simple_combined_model.pkl')
        imputer = joblib.load('./model/simple_imputer.pkl')
        scaler = joblib.load('./model/simple_scaler.pkl')
        
        with open('./model/simple_feature_info.json', 'r') as f:
            feature_info = json.load(f)
        
        print("✓ 模型加载成功")
    except Exception as e:
        print(f"✗ 模型加载失败: {e}")
        return None
    
    # 测试数据集配置
    test_configs = [
        {
            'name': 'Test Set 1',
            'file': './data/feature-encoded/for-train-test/test1-closeby6-encoded-feat146.csv'
        },
        {
            'name': 'Test Set 2', 
            'file': './data/feature-encoded/for-train-test/test2-closeby6-encoded-feat146.csv'
        }
    ]
    
    results = []
    
    for config in test_configs:
        print(f"\n--- 测试 {config['name']} ---")
        
        # 检查文件是否存在
        if not os.path.exists(config['file']):
            print(f"✗ 测试文件不存在: {config['file']}")
            continue
        
        try:
            # 2. 加载测试数据
            test_data, _ = rename_with_feat40(config['file'])
            print(f"✓ 加载测试数据: {test_data.shape}")
            
            # 3. 获取真实标签
            if '#class' in test_data.columns:
                true_labels = test_data['#class'].values
            else:
                print("✗ 未找到标签列")
                continue
            
            print(f"✓ 标签分布: 正样本={sum(true_labels)}, 负样本={len(true_labels)-sum(true_labels)}")
            
            # 4. 处理传统特征
            traditional_features = feature_info['traditional_features']
            X_traditional = test_data[traditional_features]
            X_traditional.replace('na', np.nan, inplace=True)
            X_traditional = X_traditional.astype(float, errors='ignore')
            print(f"✓ 传统特征形状: {X_traditional.shape}")
            
            # 5. 创建零序列特征（因为没有真实的测试序列）
            n_samples = len(X_traditional)
            seq_feature_names = feature_info['sequence_features']
            zero_seq_features = np.zeros((n_samples, len(seq_feature_names)))
            
            print(f"⚠️  使用零序列特征 (因为缺少测试序列): {zero_seq_features.shape}")
            
            # 6. 组合特征
            seq_df = pd.DataFrame(zero_seq_features, columns=seq_feature_names, index=X_traditional.index)
            combined_features = pd.concat([X_traditional, seq_df], axis=1)
            print(f"✓ 组合特征形状: {combined_features.shape}")
            
            # 7. 预处理
            X_imputed = imputer.transform(combined_features)
            X_scaled = scaler.transform(X_imputed)
            print("✓ 预处理完成")
            
            # 8. 预测
            predictions = model.predict_proba(X_scaled)[:, 1]
            print("✓ 预测完成")
            
            # 9. 评估
            metrics = metricsScores(true_labels, predictions)
            metrics_names = ['Sen', 'Spe', 'Pre', 'F1', 'MCC', 'ACC', 'AUC', 'AUPR']
            
            print(f"\n{config['name']} 结果 (仅传统特征):")
            for name, value in zip(metrics_names, metrics[:8]):
                print(f"  {name}: {value:.4f}")
            
            # 构建结果
            result = {
                'test_name': config['name'],
                'n_samples': n_samples,
                'label_distribution': {
                    'positive': int(sum(true_labels)),
                    'negative': int(len(true_labels) - sum(true_labels))
                },
                'metrics': dict(zip(metrics_names, metrics[:8])),
                'feature_info': {
                    'traditional_features': len(traditional_features),
                    'sequence_features': len(seq_feature_names),
                    'total_features': len(combined_features.columns),
                    'note': 'Zero sequence features used (no test sequences available)'
                }
            }
            
            results.append(result)
            
        except Exception as e:
            print(f"✗ 测试{config['name']}失败: {e}")
            continue
    
    return results

def analyze_sequence_feature_importance():
    """分析序列特征的重要性"""
    print("\n=== 序列特征重要性分析 ===")
    
    try:
        # 加载模型
        model = joblib.load('./model/simple_combined_model.pkl')
        
        with open('./model/simple_feature_info.json', 'r') as f:
            feature_info = json.load(f)
        
        # 获取特征重要性
        feature_importance = model.feature_importances_
        all_features = feature_info['all_features']
        
        # 创建重要性DataFrame
        importance_df = pd.DataFrame({
            'feature': all_features,
            'importance': feature_importance
        }).sort_values('importance', ascending=False)
        
        # 分析传统特征 vs 序列特征
        traditional_features = feature_info['traditional_features']
        sequence_features = feature_info['sequence_features']
        
        importance_df['feature_type'] = importance_df['feature'].apply(
            lambda x: 'sequence' if x in sequence_features else 'traditional'
        )
        
        # 统计
        traditional_importance = importance_df[importance_df['feature_type'] == 'traditional']['importance'].sum()
        sequence_importance = importance_df[importance_df['feature_type'] == 'sequence']['importance'].sum()
        
        print(f"特征重要性分布:")
        print(f"  传统特征总重要性: {traditional_importance:.4f} ({traditional_importance/(traditional_importance+sequence_importance)*100:.1f}%)")
        print(f"  序列特征总重要性: {sequence_importance:.4f} ({sequence_importance/(traditional_importance+sequence_importance)*100:.1f}%)")
        
        print(f"\nTop 10 最重要特征:")
        for i, (_, row) in enumerate(importance_df.head(10).iterrows()):
            print(f"  {i+1:2d}. {row['feature']:<25} {row['importance']:.4f} ({row['feature_type']})")
        
        print(f"\nTop 5 序列特征:")
        seq_features = importance_df[importance_df['feature_type'] == 'sequence'].head(5)
        for i, (_, row) in enumerate(seq_features.iterrows()):
            print(f"  {i+1}. {row['feature']:<25} {row['importance']:.4f}")
        
        # 保存重要性分析
        importance_df.to_csv('./results/feature_importance_analysis.csv', index=False)
        print(f"\n✓ 特征重要性分析已保存到 ./results/feature_importance_analysis.csv")
        
        return importance_df
        
    except Exception as e:
        print(f"✗ 特征重要性分析失败: {e}")
        return None

def create_honest_evaluation_report():
    """创建诚实的评估报告"""
    print("\n=== 创建诚实评估报告 ===")
    
    report = {
        'evaluation_type': 'Honest Model Evaluation',
        'timestamp': pd.Timestamp.now().isoformat(),
        'limitations': {
            'missing_test_sequences': True,
            'data_leakage_risk': 'Previous test used training sequences',
            'current_approach': 'Zero sequence features for fair comparison'
        },
        'model_info': {
            'architecture': 'Traditional features + Sequence features + XGBoost',
            'traditional_features': 146,
            'sequence_features': 20,
            'total_features': 166,
            'training_samples': 3494
        },
        'findings': {
            'sequence_feature_contribution': 'To be determined with real test sequences',
            'current_limitation': 'Cannot evaluate sequence features without test sequences',
            'recommendation': 'Need test sequences for proper evaluation'
        }
    }
    
    # 保存报告
    with open('./results/honest_evaluation_report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    print("✓ 诚实评估报告已保存到 ./results/honest_evaluation_report.json")
    
    return report

def main():
    """主函数"""
    print("🔍 修正版模型测试 - 诚实评估")
    print("=" * 50)
    
    print("⚠️  重要说明:")
    print("   由于缺少测试集的DNA序列文件，之前的测试结果存在数据泄露问题")
    print("   本次测试将使用零序列特征进行公平比较")
    print("   这相当于仅使用传统特征的性能")
    
    # 检查模型文件
    model_files = [
        './model/simple_combined_model.pkl',
        './model/simple_imputer.pkl', 
        './model/simple_scaler.pkl',
        './model/simple_feature_info.json'
    ]
    
    missing_files = [f for f in model_files if not os.path.exists(f)]
    if missing_files:
        print("缺少以下模型文件:")
        for f in missing_files:
            print(f"  - {f}")
        print("请先运行 simple_train.py 进行训练")
        return 1
    
    # 1. 仅传统特征测试
    results = test_traditional_features_only()
    
    # 2. 特征重要性分析
    importance_df = analyze_sequence_feature_importance()
    
    # 3. 创建诚实评估报告
    honest_report = create_honest_evaluation_report()
    
    # 4. 保存结果
    if results:
        print("\n=== 保存修正后的测试结果 ===")
        os.makedirs('./results', exist_ok=True)
        
        # 保存详细结果
        with open('./results/corrected_test_results.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        # 保存CSV摘要
        summary_data = []
        for result in results:
            row = {'test_set': result['test_name'], 'n_samples': result['n_samples']}
            row.update(result['metrics'])
            row['note'] = 'Zero sequence features (no test sequences)'
            summary_data.append(row)
        
        if summary_data:
            df_summary = pd.DataFrame(summary_data)
            df_summary.to_csv('./results/corrected_test_summary.csv', index=False)
        
        print("✓ 修正后的结果已保存到:")
        print("  - ./results/corrected_test_results.json")
        print("  - ./results/corrected_test_summary.csv")
        
        # 显示结果
        print("\n=== 修正后的测试结果 ===")
        for result in results:
            print(f"\n{result['test_name']} (仅传统特征等效):")
            print(f"  样本数: {result['n_samples']}")
            print(f"  AUC: {result['metrics']['AUC']:.4f}")
            print(f"  AUPR: {result['metrics']['AUPR']:.4f}")
            print(f"  ACC: {result['metrics']['ACC']:.4f}")
            print(f"  F1: {result['metrics']['F1']:.4f}")
    
    print(f"\n📋 总结:")
    print(f"1. ✅ 发现了之前测试中的数据泄露问题")
    print(f"2. ✅ 使用零序列特征进行了公平测试")
    print(f"3. ✅ 分析了序列特征的重要性")
    print(f"4. ⚠️  需要真实的测试序列才能评估序列特征的真实贡献")
    print(f"5. 💡 建议: 获取测试集对应的DNA序列文件进行完整评估")
    
    return 0

if __name__ == "__main__":
    exit(main())
