import pandas as pd
import numpy as np
import joblib
import json
import os
from pathlib import Path
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import MinMaxScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score
from xgboost import XGBClassifier
import warnings
warnings.filterwarnings("ignore")

# 兼容不同版本的sklearn
try:
    from sklearn.impute import SimpleImputer
except ImportError:
    try:
        from sklearn.preprocessing import Imputer as SimpleImputer
    except ImportError:
        # 如果都没有，使用pandas的fillna作为替代
        SimpleImputer = None

# 确保模型目录存在
Path("model").mkdir(exist_ok=True)

class SoftEnsembleXGBRF:
    """软集成XGBoost和随机森林分类器"""
    
    def __init__(self, use_gpu=True):
        self.use_gpu = use_gpu
        self.xgb_model = None
        self.rf_model = None
        self.ensemble_weights = None
        self.imputer = None
        self.scaler = None
        self.feature_names = None
        self.is_fitted = False
        
    def _init_models(self):
        """初始化基础模型"""
        if self.use_gpu:
            self.xgb_model = XGBClassifier(
                n_estimators=1000,
                learning_rate=0.1,
                max_depth=6,
                tree_method='gpu_hist',
                gpu_id=0,
                random_state=42,
                n_jobs=1,
                eval_metric='logloss'
            )
        else:
            self.xgb_model = XGBClassifier(
                n_estimators=1000,
                learning_rate=0.1,
                max_depth=6,
                random_state=42,
                n_jobs=-1,
                eval_metric='logloss'
            )
        
        self.rf_model = RandomForestClassifier(
            n_estimators=1000,
            max_depth=10,
            min_samples_split=5,
            min_samples_leaf=2,
            random_state=42,
            n_jobs=-1
        )
    
    def _preprocess_data(self, X, y=None, fit=False):
        """数据预处理 - 兼容不同sklearn版本"""
        # 处理缺失值
        X_processed = X.copy()
        X_processed.replace('na', np.nan, inplace=True)
        X_processed = X_processed.astype(float, errors='ignore')
        
        if fit:
            # 训练时拟合预处理器
            if SimpleImputer is not None:
                # 使用sklearn的Imputer
                if hasattr(SimpleImputer, '__init__'):
                    # 新版本sklearn
                    self.imputer = SimpleImputer(strategy='median')
                else:
                    # 旧版本sklearn
                    self.imputer = SimpleImputer(strategy='median', axis=0)
                X_imputed = self.imputer.fit_transform(X_processed)
            else:
                # 使用pandas的fillna作为备选
                print("警告: 使用pandas fillna替代sklearn Imputer")
                self.median_values = X_processed.median()
                X_imputed = X_processed.fillna(self.median_values).values
                self.imputer = 'pandas_fillna'
            
            self.scaler = MinMaxScaler()
            X_scaled = self.scaler.fit_transform(X_imputed)
        else:
            # 预测时使用已拟合的预处理器
            if self.imputer == 'pandas_fillna':
                X_imputed = X_processed.fillna(self.median_values).values
            else:
                X_imputed = self.imputer.transform(X_processed)
            X_scaled = self.scaler.transform(X_imputed)
        
        return pd.DataFrame(X_scaled, columns=X.columns)
    
    def fit(self, X, y, validation_split=0.2):
        """训练软集成模型"""
        print("开始训练软集成XGBoost+随机森林模型...")
        
        # 保存特征名称
        self.feature_names = list(X.columns)
        
        # 初始化模型
        self._init_models()
        
        # 数据预处理
        X_processed = self._preprocess_data(X, y, fit=True)
        
        # 数据分割
        X_train, X_val, y_train, y_val = train_test_split(
            X_processed, y, test_size=validation_split, random_state=42, stratify=y
        )
        
        print(f"训练集大小: {X_train.shape}, 验证集大小: {X_val.shape}")
        print(f"训练集标签分布: {y_train.value_counts().to_dict()}")
        
        # 训练XGBoost
        print("训练XGBoost模型...")
        self.xgb_model.fit(
            X_train, y_train,
            eval_set=[(X_val, y_val)],
            early_stopping_rounds=50,
            verbose=False
        )
        
        # 训练随机森林
        print("训练随机森林模型...")
        self.rf_model.fit(X_train, y_train)
        
        # 获取验证集预测概率
        xgb_probs = self.xgb_model.predict_proba(X_val)[:, 1]
        rf_probs = self.rf_model.predict_proba(X_val)[:, 1]
        
        # 计算最优权重（基于验证集AUC）
        print("计算最优集成权重...")
        best_auc = 0
        best_weights = [0.5, 0.5]
        
        for w1 in np.arange(0.1, 1.0, 0.1):
            w2 = 1 - w1
            ensemble_probs = w1 * xgb_probs + w2 * rf_probs
            auc = roc_auc_score(y_val, ensemble_probs)
            
            if auc > best_auc:
                best_auc = auc
                best_weights = [w1, w2]
        
        self.ensemble_weights = best_weights
        print(f"最优权重 - XGBoost: {best_weights[0]:.3f}, RF: {best_weights[1]:.3f}")
        print(f"验证集AUC: {best_auc:.4f}")
        
        # 评估各模型性能
        xgb_auc = roc_auc_score(y_val, xgb_probs)
        rf_auc = roc_auc_score(y_val, rf_probs)
        
        print(f"XGBoost单独AUC: {xgb_auc:.4f}")
        print(f"随机森林单独AUC: {rf_auc:.4f}")
        print(f"软集成AUC: {best_auc:.4f}")
        
        self.is_fitted = True
        
        return {
            'xgb_auc': xgb_auc,
            'rf_auc': rf_auc,
            'ensemble_auc': best_auc,
            'weights': best_weights
        }
    
    def predict_proba(self, X):
        """预测概率"""
        if not self.is_fitted:
            raise ValueError("模型尚未训练，请先调用fit方法")
        
        # 数据预处理
        X_processed = self._preprocess_data(X, fit=False)
        
        # 获取各模型预测概率
        xgb_probs = self.xgb_model.predict_proba(X_processed)[:, 1]
        rf_probs = self.rf_model.predict_proba(X_processed)[:, 1]
        
        # 软集成
        ensemble_probs = (self.ensemble_weights[0] * xgb_probs + 
                         self.ensemble_weights[1] * rf_probs)
        
        return ensemble_probs
    
    def predict(self, X, threshold=0.5):
        """预测类别"""
        probs = self.predict_proba(X)
        return (probs >= threshold).astype(int)

def save_soft_ensemble_model(model, model_name, performance_metrics):
    """保存软集成模型为.model格式"""
    # 处理imputer的保存
    if hasattr(model, 'median_values'):
        # 如果使用pandas fillna，保存中位数值
        imputer_data = {
            'type': 'pandas_fillna',
            'median_values': model.median_values
        }
    else:
        imputer_data = {
            'type': 'sklearn_imputer',
            'imputer': model.imputer
        }
    
    model_data = {
        'xgb_model': model.xgb_model,
        'rf_model': model.rf_model,
        'ensemble_weights': model.ensemble_weights,
        'imputer_data': imputer_data,
        'scaler': model.scaler,
        'feature_names': model.feature_names,
        'use_gpu': model.use_gpu,
        'performance_metrics': performance_metrics,
        'model_type': 'SoftEnsembleXGBRF'
    }
    
    model_path = f"model/{model_name}.model"
    joblib.dump(model_data, model_path)
    
    absolute_path = Path(model_path).absolute()
    file_size = Path(model_path).stat().st_size / 1024
    
    print(f"✓ 软集成模型已保存:")
    print(f"  相对路径: {model_path}")
    print(f"  绝对路径: {absolute_path}")
    print(f"  文件大小: {file_size:.2f} KB")
    
    return model_path

def load_soft_ensemble_model(model_path):
    """加载软集成模型"""
    model_data = joblib.load(model_path)
    
    # 重建模型
    model = SoftEnsembleXGBRF(use_gpu=model_data['use_gpu'])
    model.xgb_model = model_data['xgb_model']
    model.rf_model = model_data['rf_model']
    model.ensemble_weights = model_data['ensemble_weights']
    
    # 处理imputer的加载
    imputer_data = model_data.get('imputer_data', model_data.get('imputer'))
    if isinstance(imputer_data, dict):
        if imputer_data['type'] == 'pandas_fillna':
            model.imputer = 'pandas_fillna'
            model.median_values = imputer_data['median_values']
        else:
            model.imputer = imputer_data['imputer']
    else:
        model.imputer = imputer_data
    
    model.scaler = model_data['scaler']
    model.feature_names = model_data['feature_names']
    model.is_fitted = True
    
    return model, model_data['performance_metrics']

def find_training_data():
    """查找可用的训练数据文件"""
    possible_files = [
        './data/feature-encoded/train_pos_neg_closeby6_20250118-manual-del.csv',
        './data/feature-encoded/for-train-test/train-closeby6-encoded-feat146.csv',
        './data/feature-encoded/train_pos_neg_closeby6.csv'
    ]
    
    for file_path in possible_files:
        if os.path.exists(file_path):
            print(f"找到训练数据文件: {file_path}")
            return file_path
    
    # 如果没找到，列出data目录下的文件
    data_dir = './data/feature-encoded'
    if os.path.exists(data_dir):
        print(f"在 {data_dir} 目录下找到以下文件:")
        for file in os.listdir(data_dir):
            if file.endswith('.csv'):
                print(f"  - {file}")
        
        # 尝试子目录
        for_train_test_dir = os.path.join(data_dir, 'for-train-test')
        if os.path.exists(for_train_test_dir):
            print(f"在 {for_train_test_dir} 目录下找到以下文件:")
            for file in os.listdir(for_train_test_dir):
                if file.endswith('.csv'):
                    print(f"  - {file}")
    
    raise FileNotFoundError("未找到训练数据文件，请检查数据路径")

def train_soft_ensemble_with_feat73(use_gpu=True):
    """使用73维最优特征训练软集成模型"""
    
    print("=" * 60)
    print("软集成XGBoost+随机森林训练 (73维最优特征)")
    print("=" * 60)
    
    # 检查sklearn版本
    import sklearn
    print(f"sklearn版本: {sklearn.__version__}")
    
    # 1. 读取73维最优特征名称
    print("读取73维最优特征...")
    feat_file = './results/AAAA_featureSelect/feat_importance_sorted_rename.csv'
    if not os.path.exists(feat_file):
        print(f"特征文件不存在: {feat_file}")
        print("尝试查找其他特征文件...")
        
        # 查找可能的特征文件
        possible_feat_files = [
            './results/feat_importance_sorted_rename.csv',
            './results/AAAA_featureSelect/feat_importance_sorted.csv',
            './data/feat146.json'
        ]
        
        feat_file = None
        for f in possible_feat_files:
            if os.path.exists(f):
                feat_file = f
                print(f"找到特征文件: {feat_file}")
                break
        
        if feat_file is None:
            print("未找到特征文件，使用默认的73个特征名称")
            feat73_cols = [f"feat_{i+1}" for i in range(73)]
        else:
            if feat_file.endswith('.json'):
                with open(feat_file, 'r') as f:
                    all_feats = json.load(f)
                feat73_cols = all_feats[:73]
            else:
                feat146_cols = pd.read_csv(feat_file)
                feat73_cols = feat146_cols['feat_name'][:73].tolist()
    else:
        feat146_cols = pd.read_csv(feat_file)
        feat73_cols = feat146_cols['feat_name'][:73].tolist()
    
    print(f"使用特征数量: {len(feat73_cols)}")
    
    # 2. 读取训练数据
    print("读取训练数据...")
    train_file = find_training_data()
    train_data = pd.read_csv(train_file)
    print(f"原始训练数据形状: {train_data.shape}")
    print(f"数据列名: {list(train_data.columns)}")
    
    # 3. 检查是否需要重命名特征
    if 'feat_name' not in train_data.columns and len([col for col in train_data.columns if col.isdigit()]) > 50:
        print("检测到数字列名，尝试重命名特征...")
        try:
            from utils import rename_with_feat40
            train_data, _ = rename_with_feat40(train_file)
            print("特征重命名成功")
        except Exception as e:
            print(f"特征重命名失败: {e}")
    
    # 4. 检查特征是否存在
    missing_features = [feat for feat in feat73_cols if feat not in train_data.columns]
    if missing_features:
        print(f"缺失的特征数量: {len(missing_features)}")
        print(f"缺失特征示例: {missing_features[:5]}")
        
        # 使用可用的特征
        available_features = [feat for feat in feat73_cols if feat in train_data.columns]
        if len(available_features) < 10:
            print("可用特征太少，尝试使用数据中的数值特征...")
            numeric_cols = train_data.select_dtypes(include=[np.number]).columns.tolist()
            # 排除标签列
            label_candidates = ['label', '#class', 'class', 'target', 'y']
            for label_candidate in label_candidates:
                if label_candidate in numeric_cols:
                    numeric_cols.remove(label_candidate)
            
            feat73_cols = numeric_cols[:73] if len(numeric_cols) >= 73 else numeric_cols
            print(f"使用数值特征: {len(feat73_cols)}个")
        else:
            feat73_cols = available_features
            print(f"使用可用特征: {len(feat73_cols)}个")
    
    # 5. 确定标签列
    label_candidates = ['label', '#class', 'class', 'target', 'y']
    label_col = None
    for candidate in label_candidates:
        if candidate in train_data.columns:
            label_col = candidate
            break
    
    if label_col is None:
        # 使用最后一列作为标签
        label_col = train_data.columns[-1]
        print(f"使用最后一列作为标签: {label_col}")
    else:
        print(f"使用标签列: {label_col}")
    
    # 6. 准备特征和标签
    X = train_data[feat73_cols]
    y = train_data[label_col]
    
    print(f"特征数据形状: {X.shape}")
    print(f"标签分布: {y.value_counts().to_dict()}")
    
    # 7. 训练软集成模型
    model = SoftEnsembleXGBRF(use_gpu=use_gpu)
    performance = model.fit(X, y, validation_split=0.2)
    
    # 8. 交叉验证评估
    print("\n进行5折交叉验证...")
    X_processed = model._preprocess_data(X, fit=False)
    
    # XGBoost交叉验证
    xgb_cv_scores = cross_val_score(model.xgb_model, X_processed, y, cv=5, scoring='roc_auc')
    rf_cv_scores = cross_val_score(model.rf_model, X_processed, y, cv=5, scoring='roc_auc')
    
    print(f"XGBoost CV AUC: {xgb_cv_scores.mean():.4f} ± {xgb_cv_scores.std():.4f}")
    print(f"随机森林 CV AUC: {rf_cv_scores.mean():.4f} ± {rf_cv_scores.std():.4f}")
    
    # 9. 保存性能指标
    final_performance = {
        **performance,
        'xgb_cv_auc_mean': xgb_cv_scores.mean(),
        'xgb_cv_auc_std': xgb_cv_scores.std(),
        'rf_cv_auc_mean': rf_cv_scores.mean(),
        'rf_cv_auc_std': rf_cv_scores.std(),
        'feature_count': len(feat73_cols),
        'training_samples': len(X),
        'sklearn_version': sklearn.__version__,
        'training_file': train_file
    }
    
    # 10. 保存模型
    model_name = f"soft_ensemble_xgb_rf_feat{len(feat73_cols)}{'_gpu' if use_gpu else ''}"
    model_path = save_soft_ensemble_model(model, model_name, final_performance)
    
    # 11. 保存特征名称
    with open(f'./model/soft_ensemble_feat{len(feat73_cols)}_names.json', 'w') as f:
        json.dump(feat73_cols, f, indent=2)
    
    # 12. 保存训练报告
    report = {
        'model_name': model_name,
        'model_path': model_path,
        'feature_count': len(feat73_cols),
        'training_samples': len(X),
        'performance_metrics': final_performance,
        'ensemble_weights': {
            'xgboost': model.ensemble_weights[0],
            'random_forest': model.ensemble_weights[1]
        },
        'use_gpu': use_gpu,
        'sklearn_version': sklearn.__version__,
        'training_file': train_file,
        'features_used': feat73_cols
    }
    
    with open('./model/soft_ensemble_training_report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n✅ 训练完成！")
    print(f"模型文件: {model_path}")
    print(f"特征文件: ./model/soft_ensemble_feat{len(feat73_cols)}_names.json")
    print(f"训练报告: ./model/soft_ensemble_training_report.json")
    print(f"最终集成AUC: {performance['ensemble_auc']:.4f}")
    print(f"实际使用特征数: {len(feat73_cols)}")
    
    return model, model_path, final_performance

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='训练软集成XGBoost+随机森林模型')
    parser.add_argument('--gpu', action='store_true', help='使用GPU加速XGBoost')
    parser.add_argument('--no-gpu', dest='gpu', action='store_false', help='使用CPU训练')
    parser.set_defaults(gpu=True)
    
    args = parser.parse_args()
    
    try:
        model, model_path, performance = train_soft_ensemble_with_feat73(use_gpu=args.gpu)
        print(f"\n🎉 训练成功完成！")
        print(f"模型已保存至: {model_path}")
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

