# MFDSMC

We developed a novel machine learning framework, **MFDSMC**, for predicting cancer driver synonymous mutations. MFDSMC does not included other tool's prediction as features. We used the same  benchmark datasets  as PredDSMC and enriched the features encoded with [synMall](https://bioinfo.ahu.edu.cn/synMall/#/home). The evaluation results on benchmark test datasets demonstrate that MFDSMC outperforms existing methods, making it a valuable tool for researchers in identifying functional synonymous mutations in cancer.
The details are summarized as follows.

* data: it contains the original mutation data in folder `./vcf` and the processed data used in the project in folder `./feature-encoded`.
* code: it contains the code used in the project, including the process of training and testing the model.
  
  - for_test.py：load the model and test the data.
  - utils.py: functions used in the project.
* model: models used and saved during the project. The final model is `model\xgb_clf_mod_feat73.model`.
* results: feature importance and the optimal feature subset information.

## Environment setup

We recommend you to build a python virtual environment with [Anaconda](https://docs.anaconda.com/anaconda/).

First create a conda environment for the project:`conda create -n mfdsmc python=3.11`.

Then activate the environment:`conda activate mfdsmc`.

Then install the required packages: `pip install -r requirements.txt`.

## Usage

Please see the template data at `/data` ,it contains various characteristic data and synonymous mutations in the form of VCF. If you are trying to using MFDSMC with your own data, please process you data into the same format as it.

## Examples

### Original Model
You can run the following command to test the model with data of Test Set I: `python for_test.py`. If you haved encoded your mutations with the aforementioned features, just replace the `./data/feature-encoded/for-train-test/test1-closeby6-encoded-feat146.csv` with your own data.

### CNN+XGBoost Enhanced Model (NEW!)

We have developed an enhanced version that combines CNN-extracted DNA sequence features with traditional features:

#### Quick Demo
```bash
python code/cnn_xgb_demo.py
```

#### Full Training and Testing Pipeline
```bash
# Run complete pipeline (demo + train + test + visualize)
python run_cnn_xgb_pipeline.py --mode full

# Or run individual steps:
python run_cnn_xgb_pipeline.py --mode demo      # Quick demonstration
python run_cnn_xgb_pipeline.py --mode train     # Train CNN+XGBoost model
python run_cnn_xgb_pipeline.py --mode test      # Test the model
python run_cnn_xgb_pipeline.py --mode visualize # Generate visualizations
```

#### Key Features of CNN+XGBoost Model:
- **DNA Sequence Processing**: Uses CNN to extract features from 128bp DNA sequences
- **Feature Fusion**: Combines 146 traditional features + 128 CNN features = 274 total features
- **GPU Acceleration**: Supports GPU training for faster performance
- **Enhanced Prediction**: Improved accuracy by capturing sequence patterns and motifs

#### Model Architecture:
```
DNA Sequence (128bp) → One-hot Encoding → CNN → 128D Features
                                                      ↓
Traditional Features (146D) → Preprocessing → [Concatenate] → XGBoost → Prediction
```

For detailed documentation, see [CNN_XGB_README.md](CNN_XGB_README.md)
