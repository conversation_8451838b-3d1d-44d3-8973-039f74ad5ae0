#!/usr/bin/env python3
"""
简化的测试脚本 - 测试训练好的组合模型
"""

import sys
import os
import pandas as pd
import numpy as np
import json
import joblib

# 添加code目录到路径
sys.path.append('./code')
sys.path.append('.')

try:
    from utils import rename_with_feat40, metricsScores
    print("✓ 成功导入utils模块")
except ImportError as e:
    print(f"✗ 导入utils失败: {e}")
    sys.exit(1)

def load_dna_sequences_simple(pos_file, neg_file):
    """简单的DNA序列加载函数"""
    sequences = []
    labels = []
    
    print(f"加载正样本序列: {pos_file}")
    try:
        with open(pos_file, 'r') as f:
            for line in f:
                parts = line.strip().split('\t')
                if len(parts) >= 5:
                    seq = parts[4]
                    sequences.append(seq)
                    labels.append(1)
        print(f"✓ 加载了 {sum(labels)} 个正样本")
    except Exception as e:
        print(f"✗ 加载正样本失败: {e}")
        return [], []
    
    print(f"加载负样本序列: {neg_file}")
    try:
        with open(neg_file, 'r') as f:
            for line in f:
                parts = line.strip().split('\t')
                if len(parts) >= 5:
                    seq = parts[4]
                    sequences.append(seq)
                    labels.append(0)
        print(f"✓ 加载了 {len(labels) - sum(labels)} 个负样本")
    except Exception as e:
        print(f"✗ 加载负样本失败: {e}")
        return [], []
    
    return sequences, labels

def encode_dna_sequence_simple(sequence, seq_length=128):
    """简单的DNA序列编码函数"""
    # 确保序列长度为128bp
    if len(sequence) > seq_length:
        sequence = sequence[:seq_length]
    elif len(sequence) < seq_length:
        sequence = sequence + 'N' * (seq_length - len(sequence))
    
    # 简单的数值编码: A=0, T=1, G=2, C=3, N=4
    base_to_num = {'A': 0, 'T': 1, 'G': 2, 'C': 3, 'N': 4}
    encoded = []
    
    for base in sequence.upper():
        encoded.append(base_to_num.get(base, 4))
    
    return encoded

def extract_sequence_features_simple(sequences):
    """简单的序列特征提取"""
    print("提取序列特征...")
    
    features = []
    for seq in sequences:
        # 编码序列
        encoded = encode_dna_sequence_simple(seq)
        
        # 提取简单的统计特征
        seq_features = []
        
        # 碱基组成
        for base_num in range(5):  # A, T, G, C, N
            count = encoded.count(base_num)
            seq_features.append(count / len(encoded))
        
        # GC含量
        gc_count = encoded.count(2) + encoded.count(3)  # G + C
        seq_features.append(gc_count / len(encoded))
        
        # 连续碱基统计
        for base_num in range(4):
            max_consecutive = 0
            current_consecutive = 0
            for e in encoded:
                if e == base_num:
                    current_consecutive += 1
                    max_consecutive = max(max_consecutive, current_consecutive)
                else:
                    current_consecutive = 0
            seq_features.append(max_consecutive / len(encoded))
        
        # 二核苷酸频率 (简化版)
        dinucleotides = ['AA', 'AT', 'AG', 'AC', 'TT', 'TG', 'TC', 'GG', 'GC', 'CC']
        seq_str = ''.join([['A', 'T', 'G', 'C', 'N'][e] for e in encoded])
        for dinuc in dinucleotides:
            count = seq_str.count(dinuc)
            seq_features.append(count / max(1, len(seq_str) - 1))
        
        features.append(seq_features)
    
    return np.array(features)

def test_on_dataset(test_name, test_file, pos_seq_file, neg_seq_file):
    """在指定测试集上测试模型"""
    print(f"\n=== 测试 {test_name} ===")
    
    # 1. 加载模型和预处理器
    print("1. 加载训练好的模型...")
    try:
        model = joblib.load('./model/simple_combined_model.pkl')
        imputer = joblib.load('./model/simple_imputer.pkl')
        scaler = joblib.load('./model/simple_scaler.pkl')
        
        with open('./model/simple_feature_info.json', 'r') as f:
            feature_info = json.load(f)
        
        print("✓ 模型加载成功")
    except Exception as e:
        print(f"✗ 模型加载失败: {e}")
        return None
    
    # 2. 加载测试数据
    print("2. 加载测试数据...")
    
    # 检查测试文件是否存在
    if not os.path.exists(test_file):
        print(f"✗ 测试文件不存在: {test_file}")
        return None
    
    try:
        test_data, _ = rename_with_feat40(test_file)
        print(f"✓ 传统特征数据形状: {test_data.shape}")
    except Exception as e:
        print(f"✗ 加载传统特征失败: {e}")
        return None
    
    # 3. 加载序列数据
    print("3. 加载序列数据...")
    
    # 检查序列文件是否存在
    if not os.path.exists(pos_seq_file) or not os.path.exists(neg_seq_file):
        print(f"✗ 序列文件不存在，使用训练序列进行演示")
        pos_seq_file = './data/seq_cosmic/train_pos_seq.txt'
        neg_seq_file = './data/seq_cosmic/train_neg_seq.txt'
    
    sequences, seq_labels = load_dna_sequences_simple(pos_seq_file, neg_seq_file)
    
    if not sequences:
        print("✗ 序列加载失败")
        return None
    
    print(f"✓ 序列数量: {len(sequences)}")
    
    # 4. 数据对齐
    print("4. 数据对齐...")
    min_len = min(len(test_data), len(sequences))
    test_data = test_data.iloc[:min_len]
    sequences = sequences[:min_len]
    seq_labels = seq_labels[:min_len]
    
    # 获取真实标签
    if '#class' in test_data.columns:
        true_labels = test_data['#class'].values
    else:
        true_labels = np.array(seq_labels)
    
    print(f"✓ 对齐后样本数: {min_len}")
    print(f"✓ 标签分布: 正样本={sum(true_labels)}, 负样本={len(true_labels)-sum(true_labels)}")
    
    # 5. 特征提取和组合
    print("5. 特征提取和组合...")
    
    # 提取序列特征
    seq_features = extract_sequence_features_simple(sequences)
    print(f"✓ 序列特征形状: {seq_features.shape}")
    
    # 处理传统特征
    traditional_features = feature_info['traditional_features']
    X_traditional = test_data[traditional_features]
    X_traditional.replace('na', np.nan, inplace=True)
    X_traditional = X_traditional.astype(float, errors='ignore')
    print(f"✓ 传统特征形状: {X_traditional.shape}")
    
    # 组合特征
    seq_feature_names = feature_info['sequence_features']
    seq_df = pd.DataFrame(seq_features, columns=seq_feature_names, index=X_traditional.index)
    combined_features = pd.concat([X_traditional, seq_df], axis=1)
    print(f"✓ 组合特征形状: {combined_features.shape}")
    
    # 6. 预处理
    print("6. 数据预处理...")
    X_imputed = imputer.transform(combined_features)
    X_scaled = scaler.transform(X_imputed)
    print("✓ 预处理完成")
    
    # 7. 预测
    print("7. 进行预测...")
    predictions = model.predict_proba(X_scaled)[:, 1]
    print("✓ 预测完成")
    
    # 8. 评估
    print("8. 评估结果...")
    metrics = metricsScores(true_labels, predictions)
    
    metrics_names = ['Sen', 'Spe', 'Pre', 'F1', 'MCC', 'ACC', 'AUC', 'AUPR']
    
    print(f"\n{test_name} 测试结果:")
    for name, value in zip(metrics_names, metrics[:8]):
        print(f"  {name}: {value:.4f}")
    
    # 构建结果字典
    results = {
        'test_name': test_name,
        'n_samples': min_len,
        'label_distribution': {
            'positive': int(sum(true_labels)),
            'negative': int(len(true_labels) - sum(true_labels))
        },
        'metrics': dict(zip(metrics_names, metrics[:8])),
        'feature_info': {
            'traditional_features': len(traditional_features),
            'sequence_features': seq_features.shape[1],
            'total_features': combined_features.shape[1]
        }
    }
    
    return results

def main():
    """主函数"""
    print("🧬 简化组合模型测试")
    print("=" * 50)
    
    # 检查模型文件
    model_files = [
        './model/simple_combined_model.pkl',
        './model/simple_imputer.pkl',
        './model/simple_scaler.pkl',
        './model/simple_feature_info.json'
    ]
    
    missing_files = [f for f in model_files if not os.path.exists(f)]
    if missing_files:
        print("缺少以下模型文件:")
        for f in missing_files:
            print(f"  - {f}")
        print("请先运行 simple_train.py 进行训练")
        return 1
    
    # 测试数据集配置
    test_configs = [
        {
            'name': 'Test Set 1',
            'test_file': './data/feature-encoded/for-train-test/test1-closeby6-encoded-feat146.csv',
            'pos_seq_file': './data/seq_cosmic/test1_pos_seq.txt',
            'neg_seq_file': './data/seq_cosmic/test1_neg_seq.txt'
        },
        {
            'name': 'Test Set 2',
            'test_file': './data/feature-encoded/for-train-test/test2-closeby6-encoded-feat146.csv',
            'pos_seq_file': './data/seq_cosmic/test2_pos_seq.txt',
            'neg_seq_file': './data/seq_cosmic/test2_neg_seq.txt'
        }
    ]
    
    all_results = []
    
    # 运行测试
    for config in test_configs:
        result = test_on_dataset(
            config['name'],
            config['test_file'],
            config['pos_seq_file'],
            config['neg_seq_file']
        )
        
        if result:
            all_results.append(result)
    
    # 保存结果
    if all_results:
        print("\n=== 保存测试结果 ===")
        os.makedirs('./results', exist_ok=True)
        
        # 保存详细结果
        with open('./results/simple_test_results.json', 'w') as f:
            json.dump(all_results, f, indent=2)
        
        # 保存简化的CSV结果
        summary_data = []
        for result in all_results:
            row = {'test_set': result['test_name'], 'n_samples': result['n_samples']}
            row.update(result['metrics'])
            summary_data.append(row)
        
        if summary_data:
            df_summary = pd.DataFrame(summary_data)
            df_summary.to_csv('./results/simple_test_summary.csv', index=False)
        
        print("✓ 结果已保存到:")
        print("  - ./results/simple_test_results.json")
        print("  - ./results/simple_test_summary.csv")
        
        # 显示汇总
        print("\n=== 测试结果汇总 ===")
        for result in all_results:
            print(f"\n{result['test_name']}:")
            print(f"  样本数: {result['n_samples']}")
            print(f"  AUC: {result['metrics']['AUC']:.4f}")
            print(f"  AUPR: {result['metrics']['AUPR']:.4f}")
            print(f"  ACC: {result['metrics']['ACC']:.4f}")
            print(f"  F1: {result['metrics']['F1']:.4f}")
        
        print("\n🎉 测试完成!")
        return 0
    else:
        print("\n❌ 测试失败")
        return 1

if __name__ == "__main__":
    exit(main())
