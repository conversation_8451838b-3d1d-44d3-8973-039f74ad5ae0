"""
CNN+XGBoost集成模型演示脚本
这个脚本展示如何使用CNN处理DNA序列特征并与传统特征结合进行预测
"""

import pandas as pd
import numpy as np
import json
import os
from utils import rename_with_feat40, metricsScores
from train_cnn_xgb_ensemble import CNNXGBEnsemble
from dna_cnn_module import load_dna_sequences
import warnings
warnings.filterwarnings("ignore")

def quick_demo():
    """快速演示CNN+XGBoost集成模型"""
    
    print("=== CNN+XGBoost集成模型快速演示 ===")
    print("这个演示展示如何将CNN提取的DNA序列特征与传统特征结合")
    
    # 1. 检查数据文件
    print("\n1. 检查数据文件...")
    required_files = [
        './data/feature-encoded/for-train-test/train-closeby6-encoded-feat146.csv',
        './data/feat146.json',
        './data/seq_cosmic/train_pos_seq.txt',
        './data/seq_cosmic/train_neg_seq.txt'
    ]
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    if missing_files:
        print("缺少以下文件:")
        for f in missing_files:
            print(f"  - {f}")
        print("请确保数据文件存在后再运行")
        return
    
    print("所有必需文件都存在 ✓")
    
    # 2. 加载和检查数据
    print("\n2. 加载和检查数据...")
    
    # 加载传统特征
    train_data, _ = rename_with_feat40('./data/feature-encoded/for-train-test/train-closeby6-encoded-feat146.csv')
    print(f"传统特征数据形状: {train_data.shape}")
    
    # 加载DNA序列
    sequences, seq_labels, metadata = load_dna_sequences(
        './data/seq_cosmic/train_pos_seq.txt',
        './data/seq_cosmic/train_neg_seq.txt'
    )
    print(f"DNA序列数量: {len(sequences)}")
    print(f"序列长度示例: {len(sequences[0])} bp")
    print(f"标签分布: 正样本={sum(seq_labels)}, 负样本={len(seq_labels)-sum(seq_labels)}")
    
    # 3. 初始化模型
    print("\n3. 初始化CNN+XGBoost集成模型...")
    ensemble = CNNXGBEnsemble(cnn_feature_dim=64, use_gpu=True)  # 使用较小的特征维度进行演示
    
    # 4. 演示特征提取过程
    print("\n4. 演示特征提取过程...")
    
    # 取少量样本进行演示
    demo_sequences = sequences[:100]
    demo_traditional = train_data.iloc[:100]
    demo_labels = seq_labels[:100]
    
    print(f"演示样本数: {len(demo_sequences)}")
    
    # 提取CNN特征
    print("提取CNN特征...")
    cnn_features = ensemble.cnn_extractor.extract_features(demo_sequences)
    print(f"CNN特征形状: {cnn_features.shape}")
    
    # 处理传统特征
    print("处理传统特征...")
    with open('./data/feat146.json', 'r') as f:
        feat146 = json.load(f)
    
    X_traditional = demo_traditional[feat146]
    X_traditional.replace('na', np.nan, inplace=True)
    X_traditional = X_traditional.astype(float, errors='ignore')
    print(f"传统特征形状: {X_traditional.shape}")
    
    # 组合特征
    print("组合特征...")
    cnn_feature_names = [f'cnn_feat_{i}' for i in range(cnn_features.shape[1])]
    cnn_df = pd.DataFrame(cnn_features, columns=cnn_feature_names, index=X_traditional.index)
    combined_features = pd.concat([X_traditional, cnn_df], axis=1)
    print(f"组合特征形状: {combined_features.shape}")
    print(f"特征维度提升: {X_traditional.shape[1]} → {combined_features.shape[1]} (+{cnn_features.shape[1]})")
    
    # 5. 展示特征统计
    print("\n5. 特征统计信息...")
    print("传统特征统计:")
    print(f"  - 缺失值比例: {X_traditional.isnull().sum().sum() / X_traditional.size:.2%}")
    print(f"  - 数值范围: [{X_traditional.min().min():.2f}, {X_traditional.max().max():.2f}]")
    
    print("CNN特征统计:")
    print(f"  - 特征范围: [{cnn_features.min():.4f}, {cnn_features.max():.4f}]")
    print(f"  - 特征均值: {cnn_features.mean():.4f}")
    print(f"  - 特征标准差: {cnn_features.std():.4f}")
    
    # 6. 简单的预测演示
    print("\n6. 简单预测演示...")
    print("注意: 这里使用未训练的模型进行演示，实际使用需要先训练模型")
    
    # 数据预处理
    X_imputed = ensemble.imputer.fit_transform(combined_features)
    X_scaled = ensemble.scaler.fit_transform(X_imputed)
    
    # 使用默认XGBoost进行快速训练演示
    from sklearn.model_selection import train_test_split
    X_train, X_test, y_train, y_test = train_test_split(
        X_scaled, demo_labels, test_size=0.3, random_state=42
    )
    
    ensemble.xgb_model.fit(X_train, y_train)
    predictions = ensemble.xgb_model.predict_proba(X_test)[:, 1]
    
    # 计算简单指标
    from sklearn.metrics import roc_auc_score, accuracy_score
    auc = roc_auc_score(y_test, predictions)
    acc = accuracy_score(y_test, predictions > 0.5)
    
    print(f"演示结果 (仅供参考):")
    print(f"  - AUC: {auc:.4f}")
    print(f"  - 准确率: {acc:.4f}")
    print(f"  - 测试样本数: {len(y_test)}")
    
    # 7. 使用建议
    print("\n7. 使用建议...")
    print("完整的训练和测试流程:")
    print("  1. 运行 'python train_cnn_xgb_ensemble.py' 进行完整训练")
    print("  2. 运行 'python test_cnn_xgb_ensemble.py' 进行测试评估")
    print("  3. 查看 './results/' 目录下的结果文件")
    
    print("\n模型优势:")
    print("  - 结合了传统生物信息学特征和深度学习序列特征")
    print("  - CNN能够捕获DNA序列中的局部模式和motif")
    print("  - XGBoost能够处理高维特征并提供良好的泛化性能")
    print("  - 支持GPU加速训练")
    
    print("\n=== 演示完成 ===")

def show_model_architecture():
    """展示模型架构"""
    print("\n=== CNN+XGBoost集成模型架构 ===")
    
    print("""
    输入数据:
    ├── DNA序列 (128bp)
    │   └── One-hot编码 (4 × 128)
    │       └── CNN特征提取
    │           ├── 多尺度卷积 (kernel_size: 3, 5, 7)
    │           ├── 批归一化
    │           ├── 全局最大池化
    │           └── 全连接层 → CNN特征 (128维)
    │
    └── 传统特征 (146维)
        ├── synMall注释特征
        ├── 保守性特征
        ├── 表观遗传特征
        └── 功能预测特征
    
    特征融合:
    ├── 特征拼接: [传统特征 + CNN特征] → 274维
    ├── 缺失值填充 (中位数)
    ├── MinMax归一化
    └── XGBoost分类器
        ├── GPU加速训练
        ├── 1000棵树
        └── 输出预测概率
    
    输出:
    └── 癌症驱动同义突变概率 [0, 1]
    """)

def main():
    """主函数"""
    # 显示模型架构
    show_model_architecture()
    
    # 运行快速演示
    quick_demo()

if __name__ == "__main__":
    main()
