import pandas as pd
import numpy as np
import joblib,json
from utils import rename_with_feat40, metricsScores
from sklearn.preprocessing import MinMaxScaler
from sklearn.impute import SimpleImputer
import warnings
warnings.filterwarnings("ignore")

#  提取最优特征子集名称
feat146_cols = pd.read_csv(r'C:\Users\<USER>\Desktop\MFDSMC-main\results\AAAA_featureSelect\feat_importance_sorted_rename.csv')
feat73_cols = feat146_cols['feat_name'][:73]

# 读取特征名称
with open('./data/feat146.json', 'r') as f:
    feat146 = json.load(f)

# 加载模型数据
print("加载模型...")
model_data = joblib.load(r'./model/xgb_rf_ada_ensemble_feat73_gpu.model')

print(f"模型数据类型: {type(model_data)}")

# 从字典中提取模型和预处理器
if isinstance(model_data, dict):
    print("检测到字典格式的模型文件")
    print(f"可用键: {list(model_data.keys())}")
    
    # 提取实际模型
    if 'ensemble_model' in model_data:
        model = model_data['ensemble_model']
        print("✓ 找到集成模型")
    elif 'model' in model_data:
        model = model_data['model']
        print("✓ 找到模型")
    else:
        print("❌ 未找到模型对象")
        exit()
    
    # 提取预处理器
    if 'imputer' in model_data and 'scaler' in model_data:
        imputer = model_data['imputer']
        minmax = model_data['scaler']
        print("✓ 使用模型内置的预处理器")
    else:
        # 加载独立的预处理器文件
        imputer = joblib.load('./model/xg_rf_a_imputer.pkl')
        minmax = joblib.load('./model/xg_rf_a_minmax.pkl')
        print("✓ 使用独立的预处理器文件")
    
    # 提取特征名称
    if 'feature_names' in model_data:
        model_features = model_data['feature_names']
        print(f"✓ 模型训练特征数量: {len(model_features)}")
    else:
        model_features = feat73_cols
        print("使用默认特征名称")
        
else:
    # 传统模型对象
    model = model_data
    imputer = joblib.load('./model/xg_rf_a_imputer.pkl')
    minmax = joblib.load('./model/xg_rf_a_minmax.pkl')
    model_features = feat73_cols
    print("✓ 传统模型对象")

# 读取数据
metrics = 'Sen, Spe, Pre, F1, MCC, ACC, AUC, AUPR, tn, fp, fn, tp, thres'.split(', ')

# 测试集1
print("\n=== 处理测试集1 ===")
X_test1, renamed_columns = rename_with_feat40(r'./data/feature-encoded/for-train-test/test1-closeby6-encoded-feat146.csv')
print(f"测试集1形状: {X_test1.shape}")

# 检查标签列
if 'label' in X_test1.columns:
    label_col = 'label'
elif '#class' in X_test1.columns:
    label_col = '#class'
else:
    label_col = X_test1.columns[-1]
    print(f"使用最后一列作为标签: {label_col}")

# 数据预处理
print("进行数据预处理...")
X_features1 = X_test1[feat146].copy()
X_features1.replace('na', np.nan, inplace=True)
X_features1 = X_features1.astype(float, errors='ignore')

try:
    # 使用预训练的预处理器
    X_test11 = imputer.transform(X_features1)
    X_test11 = minmax.transform(X_test11)
    X_test11 = pd.DataFrame(X_test11, columns=feat146)
    print("✓ 成功使用预训练的预处理器")
    
except Exception as e:
    print(f"预处理器失败: {e}")
    print("重新训练预处理器...")
    
    # 重新训练预处理器
    imputer_new = SimpleImputer(strategy='median')
    minmax_new = MinMaxScaler()
    
    X_test11 = imputer_new.fit_transform(X_features1)
    X_test11 = minmax_new.fit_transform(X_test11)
    X_test11 = pd.DataFrame(X_test11, columns=feat146)
    print("✓ 使用重新训练的预处理器")

# 选择73个特征
available_feat73 = [f for f in feat73_cols if f in X_test11.columns]
print(f"可用的73维特征: {len(available_feat73)}/{len(feat73_cols)}")

X_final1 = X_test11[available_feat73]

# 模型预测
print("进行预测...")
print(f"输入特征形状: {X_final1.shape}")

try:
    pred1 = model.predict_proba(X_final1.values)[:, 1]
    print(f"✓ 预测成功，预测值范围: [{pred1.min():.4f}, {pred1.max():.4f}]")
except Exception as e:
    print(f"预测失败: {e}")
    print("尝试其他预测方法...")
    
    # 尝试不同的预测方法
    if hasattr(model, 'predict'):
        pred1 = model.predict(X_final1.values)
        print("使用predict方法")
    else:
        print("❌ 模型没有可用的预测方法")
        exit()

# 计算指标
result_df1 = pd.concat([
    pd.Series(metrics),
    pd.Series(metricsScores(X_test1[label_col].values, pred1))
], axis=1).T

result_df1.columns = result_df1.iloc[0]
result_df1 = result_df1[1:]

print("\n=== 测试集1预测结果 ===")
print(result_df1.iloc[:, :-5])

# 测试集2
print("\n=== 处理测试集2 ===")
X_test2, renamed_columns = rename_with_feat40(r'./data/feature-encoded/for-train-test/test2-closeby6-encoded-feat146.csv')
print(f"测试集2形状: {X_test2.shape}")

# 数据预处理
X_features2 = X_test2[feat146].copy()
X_features2.replace('na', np.nan, inplace=True)
X_features2 = X_features2.astype(float, errors='ignore')

try:
    X_test22 = imputer.transform(X_features2)
    X_test22 = minmax.transform(X_test22)
    X_test22 = pd.DataFrame(X_test22, columns=feat146)
    print("✓ 测试集2预处理成功")
except Exception as e:
    print(f"测试集2预处理失败: {e}")
    # 使用重新训练的预处理器
    X_test22 = imputer_new.fit_transform(X_features2)
    X_test22 = minmax_new.fit_transform(X_test22)
    X_test22 = pd.DataFrame(X_test22, columns=feat146)
    print("✓ 使用重新训练的预处理器")

X_final2 = X_test22[available_feat73]

# 预测
pred2 = model.predict_proba(X_final2.values)[:, 1]
print(f"✓ 测试集2预测成功，预测值范围: [{pred2.min():.4f}, {pred2.max():.4f}]")

# 计算指标
result_df2 = pd.concat([
    pd.Series(metrics),
    pd.Series(metricsScores(X_test2[label_col].values, pred2))
], axis=1).T

result_df2.columns = result_df2.iloc[0]
result_df2 = result_df2[1:]

print("\n=== 测试集2预测结果 ===")
print(result_df2.iloc[:, :-5])

# 保存结果
result_df1.to_csv('./results/test1_xgb_rf_ada_results.csv', index=False)
result_df2.to_csv('./results/test2_xgb_rf_ada_results.csv', index=False)

print(f"\n✅ 结果已保存:")
print(f"测试集1: ./results/test1_xgb_rf_ada_results.csv")
print(f"测试集2: ./results/test2_xgb_rf_ada_results.csv")